# Options AI - Advanced Options Analytics Platform

Options AI is a sophisticated platform for options traders and investors to analyze and visualize options data, gain market insights, and make informed trading decisions.

## Features

- **Real-time Option Chain Data**: View comprehensive options data with customizable displays
- **Responsive Design**: Optimized for both desktop and mobile devices
- **IV Analysis**: Visualize and analyze implied volatility across strikes and expirations
- **OI Analysis**: Track open interest patterns and changes
- **AI Agent**: Get AI-powered insights and trading suggestions
- **User Preferences**: Customize your experience with personalized settings
- **Dark/Light Mode**: Choose your preferred theme
- **Secure Authentication**: Powered by Supabase for secure user management

## Screenshots

![Option Chain View](screenshots/option_chain.png)
![IV Analysis](screenshots/iv_analysis.png)
![AI Agent](screenshots/ai_agent.png)
![Settings](screenshots/settings.png)

## Mobile Support

The app is fully responsive and works on devices of all sizes:

- Adaptive layouts that adjust to screen dimensions
- Mobile-optimized views for complex data tables
- Bottom navigation for easy access on smaller screens
- Touch-friendly UI elements

## Tech Stack

- **Flutter**: Cross-platform UI framework
- **Riverpod**: State management
- **Go Router**: Navigation management
- **Supabase**: Backend and authentication
- **Syncfusion**: Advanced data visualization
- **Hooks Riverpod**: Composition and side-effect management

## Getting Started

### Prerequisites

- Flutter SDK 3.7.2 or higher
- Dart SDK 
- An active Supabase account

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/options_ai.git
cd options_ai
```

2. Install dependencies
```bash
flutter pub get
```

3. Create an `.env` file in the assets directory
```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Run the app
```bash
flutter run
```

## Architecture

The app follows a clean architecture approach:

- **Screens**: UI components and layouts
- **Widgets**: Reusable UI elements
- **Providers**: State management using Riverpod
- **Models**: Data structures and business logic
- **Services**: API communication and data handling
- **Router**: Navigation and routing logic
- **Config**: Application configuration

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Flutter](https://flutter.dev)
- [Riverpod](https://riverpod.dev)
- [Supabase](https://supabase.io)
- [Syncfusion Flutter Charts](https://www.syncfusion.com/flutter-widgets)

## Dependencies

- Flutter SDK ^3.7.2
- http: ^1.2.1 - For API calls
- flutter_dotenv: ^5.2.1 - For environment variables

## Project Structure

```
lib/
├── models/
│   └── option_chain.dart
├── services/
│   └── options_service.dart
├── screens/
│   └── home_screen.dart
├── widgets/
│   ├── option_chain_view.dart
│   └── search_bar.dart
└── main.dart
```

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request


- volume change from morning first data
- highest volume in a day is 100% rest if the volumes are calculated from it.
- oi column is good
- 