import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:local_auth/local_auth.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:flutter/foundation.dart';
import 'dart:io';

part 'enhanced_auth_provider.g.dart';

// Enhanced Authentication Provider
@riverpod
class EnhancedAuthNotifier extends _$EnhancedAuthNotifier {
  final firebase.FirebaseAuth _firebaseAuth = firebase.FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final LocalAuthentication _localAuth = LocalAuthentication();
  final supabase.SupabaseClient _supabase = supabase.Supabase.instance.client;

  @override
  AuthenticationState build() {
    // Listen to Firebase auth changes
    _firebaseAuth.authStateChanges().listen((user) {
      if (user != null) {
        state = AuthenticationState.authenticated(user: user, provider: _getAuthProvider(user));
      } else {
        state = const AuthenticationState.unauthenticated();
      }
    });

    return _firebaseAuth.currentUser != null
        ? AuthenticationState.authenticated(
            user: _firebaseAuth.currentUser!,
            provider: _getAuthProvider(_firebaseAuth.currentUser!),
          )
        : const AuthenticationState.unauthenticated();
  }

  // Email Authentication
  Future<void> signInWithEmail({required String email, required String password}) async {
    state = const AuthenticationState.loading();

    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(email: email, password: password);

      if (credential.user != null) {
        await _syncUserWithSupabase(credential.user!);
        state = AuthenticationState.authenticated(user: credential.user!, provider: AuthProviderType.email);
      }
    } on firebase.FirebaseAuthException catch (e) {
      state = AuthenticationState.error(message: _getErrorMessage(e.code));
    } catch (e) {
      state = const AuthenticationState.error(message: 'An unexpected error occurred');
    }
  }

  Future<void> signUpWithEmail({required String email, required String password, String? displayName}) async {
    state = const AuthenticationState.loading();

    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(email: email, password: password);

      if (credential.user != null) {
        if (displayName != null && displayName.isNotEmpty) {
          await credential.user!.updateDisplayName(displayName);
        }

        await credential.user!.sendEmailVerification();
        await _syncUserWithSupabase(credential.user!);

        state = AuthenticationState.authenticated(user: credential.user!, provider: AuthProviderType.email);
      }
    } on firebase.FirebaseAuthException catch (e) {
      state = AuthenticationState.error(message: _getErrorMessage(e.code));
    } catch (e) {
      state = const AuthenticationState.error(message: 'An unexpected error occurred');
    }
  }

  // Google Authentication
  Future<void> signInWithGoogle() async {
    state = const AuthenticationState.loading();

    try {
      if (kIsWeb) {
        final firebase.GoogleAuthProvider googleProvider = firebase.GoogleAuthProvider();
        final credential = await _firebaseAuth.signInWithPopup(googleProvider);

        if (credential.user != null) {
          await _syncUserWithSupabase(credential.user!);
          state = AuthenticationState.authenticated(user: credential.user!, provider: AuthProviderType.google);
        }
      } else {
        final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

        if (googleUser == null) {
          state = const AuthenticationState.error(message: 'Google sign-in was cancelled');
          return;
        }

        final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

        final credential = firebase.GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        final userCredential = await _firebaseAuth.signInWithCredential(credential);

        if (userCredential.user != null) {
          await _syncUserWithSupabase(userCredential.user!);
          state = AuthenticationState.authenticated(user: userCredential.user!, provider: AuthProviderType.google);
        }
      }
    } catch (e) {
      state = AuthenticationState.error(message: 'Google sign-in failed: ${e.toString()}');
    }
  }

  // Apple Authentication
  Future<void> signInWithApple() async {
    state = const AuthenticationState.loading();

    try {
      if (!Platform.isIOS && !Platform.isMacOS && !kIsWeb) {
        state = const AuthenticationState.error(message: 'Apple Sign In is not available on this platform');
        return;
      }

      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [AppleIDAuthorizationScopes.email, AppleIDAuthorizationScopes.fullName],
      );

      final oauthCredential = firebase.OAuthProvider(
        "apple.com",
      ).credential(idToken: appleCredential.identityToken, accessToken: appleCredential.authorizationCode);

      final userCredential = await _firebaseAuth.signInWithCredential(oauthCredential);

      if (userCredential.user != null) {
        if (appleCredential.givenName != null || appleCredential.familyName != null) {
          final displayName = '${appleCredential.givenName ?? ''} ${appleCredential.familyName ?? ''}'.trim();
          if (displayName.isNotEmpty) {
            await userCredential.user!.updateDisplayName(displayName);
          }
        }

        await _syncUserWithSupabase(userCredential.user!);
        state = AuthenticationState.authenticated(user: userCredential.user!, provider: AuthProviderType.apple);
      }
    } catch (e) {
      state = AuthenticationState.error(message: 'Apple sign-in failed: ${e.toString()}');
    }
  }

  // Phone Authentication
  Future<void> sendPhoneVerificationCode(String phoneNumber) async {
    state = const AuthenticationState.loading();

    try {
      await _firebaseAuth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (firebase.PhoneAuthCredential credential) async {
          final userCredential = await _firebaseAuth.signInWithCredential(credential);
          if (userCredential.user != null) {
            await _syncUserWithSupabase(userCredential.user!);
            state = AuthenticationState.authenticated(user: userCredential.user!, provider: AuthProviderType.phone);
          }
        },
        verificationFailed: (firebase.FirebaseAuthException e) {
          state = AuthenticationState.error(message: _getErrorMessage(e.code));
        },
        codeSent: (String verificationId, int? resendToken) {
          state = AuthenticationState.phoneCodeSent(verificationId: verificationId, phoneNumber: phoneNumber);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Handle timeout
        },
        timeout: const Duration(seconds: 60),
      );
    } catch (e) {
      state = AuthenticationState.error(message: 'Failed to send verification code');
    }
  }

  Future<void> verifyPhoneCode({required String verificationId, required String smsCode}) async {
    state = const AuthenticationState.loading();

    try {
      final credential = firebase.PhoneAuthProvider.credential(verificationId: verificationId, smsCode: smsCode);

      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        await _syncUserWithSupabase(userCredential.user!);
        state = AuthenticationState.authenticated(user: userCredential.user!, provider: AuthProviderType.phone);
      }
    } on firebase.FirebaseAuthException catch (e) {
      state = AuthenticationState.error(message: _getErrorMessage(e.code));
    } catch (e) {
      state = AuthenticationState.error(message: 'Phone verification failed');
    }
  }

  // Biometric Authentication
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      return false;
    }
  }

  Future<bool> authenticateWithBiometrics({String localizedReason = 'Please authenticate to access your account'}) async {
    try {
      return await _localAuth.authenticate(
        localizedReason: localizedReason,
        options: const AuthenticationOptions(biometricOnly: true, stickyAuth: true),
      );
    } catch (e) {
      return false;
    }
  }

  // Password Reset
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } catch (e) {
      state = AuthenticationState.error(message: 'Failed to send password reset email');
    }
  }

  // Sign Out
  Future<void> signOut() async {
    try {
      await Future.wait([_firebaseAuth.signOut(), _googleSignIn.signOut(), _supabase.auth.signOut()]);
      state = const AuthenticationState.unauthenticated();
    } catch (e) {
      state = AuthenticationState.error(message: 'Failed to sign out');
    }
  }

  // Helper Methods
  Future<void> _syncUserWithSupabase(firebase.User firebaseUser) async {
    try {
      final userData = {
        'id': firebaseUser.uid,
        'email': firebaseUser.email,
        'full_name': firebaseUser.displayName,
        'avatar_url': firebaseUser.photoURL,
        'phone': firebaseUser.phoneNumber,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase.from('user_profiles').upsert(userData).eq('id', firebaseUser.uid);
    } catch (e) {
      debugPrint('Error syncing user with Supabase: $e');
    }
  }

  AuthProviderType _getAuthProvider(firebase.User user) {
    for (final providerData in user.providerData) {
      switch (providerData.providerId) {
        case 'google.com':
          return AuthProviderType.google;
        case 'apple.com':
          return AuthProviderType.apple;
        case 'phone':
          return AuthProviderType.phone;
        case 'password':
          return AuthProviderType.email;
      }
    }
    return AuthProviderType.email;
  }

  String _getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'The password is too weak. Please use at least 6 characters.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled. Please contact support.';
      case 'too-many-requests':
        return 'Too many attempts. Please try again later.';
      case 'network-request-failed':
        return 'Network error. Please check your connection.';
      case 'invalid-verification-code':
        return 'Invalid verification code. Please try again.';
      case 'invalid-verification-id':
        return 'Invalid verification ID. Please request a new code.';
      case 'session-expired':
        return 'Session expired. Please sign in again.';
      default:
        return 'An error occurred. Please try again.';
    }
  }
}

// Helper providers
@riverpod
bool isEnhancedAuthenticated(IsEnhancedAuthenticatedRef ref) {
  final authState = ref.watch(enhancedAuthNotifierProvider);
  return authState is AuthenticatedState;
}

@riverpod
firebase.User? currentFirebaseUser(CurrentFirebaseUserRef ref) {
  final authState = ref.watch(enhancedAuthNotifierProvider);
  return authState is AuthenticatedState ? authState.user : null;
}

// Authentication State Classes
abstract class AuthenticationState {
  const AuthenticationState();

  const factory AuthenticationState.unauthenticated() = UnauthenticatedState;
  const factory AuthenticationState.loading() = LoadingState;
  const factory AuthenticationState.authenticated({required firebase.User user, required AuthProviderType provider}) =
      AuthenticatedState;
  const factory AuthenticationState.error({required String message}) = ErrorState;
  const factory AuthenticationState.phoneCodeSent({required String verificationId, required String phoneNumber}) =
      PhoneCodeSentState;
}

class UnauthenticatedState extends AuthenticationState {
  const UnauthenticatedState();
}

class LoadingState extends AuthenticationState {
  const LoadingState();
}

class AuthenticatedState extends AuthenticationState {
  final firebase.User user;
  final AuthProviderType provider;

  const AuthenticatedState({required this.user, required this.provider});
}

class ErrorState extends AuthenticationState {
  final String message;

  const ErrorState({required this.message});
}

class PhoneCodeSentState extends AuthenticationState {
  final String verificationId;
  final String phoneNumber;

  const PhoneCodeSentState({required this.verificationId, required this.phoneNumber});
}

// Auth Provider Types
enum AuthProviderType { email, google, apple, phone, anonymous }

extension AuthProviderTypeExtension on AuthProviderType {
  String get displayName {
    switch (this) {
      case AuthProviderType.email:
        return 'Email';
      case AuthProviderType.google:
        return 'Google';
      case AuthProviderType.apple:
        return 'Apple';
      case AuthProviderType.phone:
        return 'Phone';
      case AuthProviderType.anonymous:
        return 'Anonymous';
    }
  }
}
