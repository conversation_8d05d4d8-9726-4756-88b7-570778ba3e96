// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'options_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$atmStrikeHash() => r'eafc8754d2a96156aad47425a96b61dd2c603903';

/// See also [atmStrike].
@ProviderFor(atmStrike)
final atmStrikeProvider = AutoDisposeProvider<double>.internal(
  atmStrike,
  name: r'atmStrikeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$atmStrikeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AtmStrikeRef = AutoDisposeProviderRef<double>;
String _$isOptionsLoadingHash() => r'8479df00b96d8857fc21392f77a0622c6709166a';

/// See also [isOptionsLoading].
@ProviderFor(isOptionsLoading)
final isOptionsLoadingProvider = AutoDisposeProvider<bool>.internal(
  isOptionsLoading,
  name: r'isOptionsLoadingProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$isOptionsLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsOptionsLoadingRef = AutoDisposeProviderRef<bool>;
String _$filteredOptionChainHash() =>
    r'8e3c814b5d6fcc62ad2915e67fa5cf92332fed14';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [filteredOptionChain].
@ProviderFor(filteredOptionChain)
const filteredOptionChainProvider = FilteredOptionChainFamily();

/// See also [filteredOptionChain].
class FilteredOptionChainFamily extends Family<List<OptionData>> {
  /// See also [filteredOptionChain].
  const FilteredOptionChainFamily();

  /// See also [filteredOptionChain].
  FilteredOptionChainProvider call({double? minStrike, double? maxStrike}) {
    return FilteredOptionChainProvider(
      minStrike: minStrike,
      maxStrike: maxStrike,
    );
  }

  @override
  FilteredOptionChainProvider getProviderOverride(
    covariant FilteredOptionChainProvider provider,
  ) {
    return call(minStrike: provider.minStrike, maxStrike: provider.maxStrike);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'filteredOptionChainProvider';
}

/// See also [filteredOptionChain].
class FilteredOptionChainProvider
    extends AutoDisposeProvider<List<OptionData>> {
  /// See also [filteredOptionChain].
  FilteredOptionChainProvider({double? minStrike, double? maxStrike})
    : this._internal(
        (ref) => filteredOptionChain(
          ref as FilteredOptionChainRef,
          minStrike: minStrike,
          maxStrike: maxStrike,
        ),
        from: filteredOptionChainProvider,
        name: r'filteredOptionChainProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$filteredOptionChainHash,
        dependencies: FilteredOptionChainFamily._dependencies,
        allTransitiveDependencies:
            FilteredOptionChainFamily._allTransitiveDependencies,
        minStrike: minStrike,
        maxStrike: maxStrike,
      );

  FilteredOptionChainProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.minStrike,
    required this.maxStrike,
  }) : super.internal();

  final double? minStrike;
  final double? maxStrike;

  @override
  Override overrideWith(
    List<OptionData> Function(FilteredOptionChainRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FilteredOptionChainProvider._internal(
        (ref) => create(ref as FilteredOptionChainRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        minStrike: minStrike,
        maxStrike: maxStrike,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<List<OptionData>> createElement() {
    return _FilteredOptionChainProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FilteredOptionChainProvider &&
        other.minStrike == minStrike &&
        other.maxStrike == maxStrike;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, minStrike.hashCode);
    hash = _SystemHash.combine(hash, maxStrike.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FilteredOptionChainRef on AutoDisposeProviderRef<List<OptionData>> {
  /// The parameter `minStrike` of this provider.
  double? get minStrike;

  /// The parameter `maxStrike` of this provider.
  double? get maxStrike;
}

class _FilteredOptionChainProviderElement
    extends AutoDisposeProviderElement<List<OptionData>>
    with FilteredOptionChainRef {
  _FilteredOptionChainProviderElement(super.provider);

  @override
  double? get minStrike => (origin as FilteredOptionChainProvider).minStrike;
  @override
  double? get maxStrike => (origin as FilteredOptionChainProvider).maxStrike;
}

String _$optionsHash() => r'1d78c08d6a50bb358aa2226dd5f0e7a92aa93db6';

/// See also [Options].
@ProviderFor(Options)
final optionsProvider = NotifierProvider<Options, OptionsState>.internal(
  Options.new,
  name: r'optionsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$optionsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Options = Notifier<OptionsState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
