// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isOnboardingCompleteHash() =>
    r'd63c1fef4a09fd27690025f4697d13390b05ae48';

/// See also [isOnboardingComplete].
@ProviderFor(isOnboardingComplete)
final isOnboardingCompleteProvider = AutoDisposeProvider<bool>.internal(
  isOnboardingComplete,
  name: r'isOnboardingCompleteProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$isOnboardingCompleteHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsOnboardingCompleteRef = AutoDisposeProviderRef<bool>;
String _$userPreferencesHash() => r'7017f2843924f0b0a583514996ff2caa6d5ce18d';

/// See also [userPreferences].
@ProviderFor(userPreferences)
final userPreferencesProvider = AutoDisposeProvider<UserPreferences>.internal(
  userPreferences,
  name: r'userPreferencesProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userPreferencesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserPreferencesRef = AutoDisposeProviderRef<UserPreferences>;
String _$userProfileNotifierHash() =>
    r'f72a33f92671e257175d16b6ca432b612cf166b8';

/// See also [UserProfileNotifier].
@ProviderFor(UserProfileNotifier)
final userProfileNotifierProvider = AutoDisposeAsyncNotifierProvider<
  UserProfileNotifier,
  UserProfile?
>.internal(
  UserProfileNotifier.new,
  name: r'userProfileNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userProfileNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserProfileNotifier = AutoDisposeAsyncNotifier<UserProfile?>;
String _$onboardingStepsNotifierHash() =>
    r'7c79b31a83681c0e7574e93eb553530048fff4fe';

/// See also [OnboardingStepsNotifier].
@ProviderFor(OnboardingStepsNotifier)
final onboardingStepsNotifierProvider = AutoDisposeNotifierProvider<
  OnboardingStepsNotifier,
  List<OnboardingStep>
>.internal(
  OnboardingStepsNotifier.new,
  name: r'onboardingStepsNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$onboardingStepsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OnboardingStepsNotifier = AutoDisposeNotifier<List<OnboardingStep>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
