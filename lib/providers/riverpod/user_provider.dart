import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../models/user_profile_simple.dart';
import '../../services/user_service.dart';
import 'auth_provider.dart';

part 'user_provider.g.dart';

@riverpod
class UserProfileNotifier extends _$UserProfileNotifier {
  UserService get _userService => UserService();

  @override
  Future<UserProfile?> build() async {
    // Watch auth state to reload user profile when auth changes
    final authState = ref.watch(authProvider);

    if (authState.valueOrNull?.session == null) {
      return null;
    }

    try {
      return await _userService.getCurrentUserProfile();
    } catch (e) {
      throw Exception('Failed to load user profile: $e');
    }
  }

  Future<void> updateProfile(UserProfile profile) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      return await _userService.updateUserProfile(profile);
    });
  }

  Future<void> completeOnboarding() async {
    final currentProfile = state.valueOrNull;
    if (currentProfile == null) return;

    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      return await _userService.completeOnboarding(currentProfile.id);
    });
  }

  Future<void> updatePreferences(UserPreferences preferences) async {
    final currentProfile = state.valueOrNull;
    if (currentProfile == null) return;

    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      return await _userService.updatePreferences(currentProfile.id, preferences);
    });
  }
}

@riverpod
class OnboardingStepsNotifier extends _$OnboardingStepsNotifier {
  UserService get _userService => UserService();

  @override
  List<OnboardingStep> build() {
    return _userService.getOnboardingSteps();
  }

  void markStepCompleted(String stepId) {
    state =
        state.map((step) {
          if (step.id == stepId) {
            return step.copyWith(isCompleted: true);
          }
          return step;
        }).toList();
  }

  void resetSteps() {
    state = _userService.getOnboardingSteps();
  }
}

@riverpod
bool isOnboardingComplete(IsOnboardingCompleteRef ref) {
  final userProfile = ref.watch(userProfileNotifierProvider);
  return userProfile.valueOrNull?.isOnboarded ?? false;
}

@riverpod
UserPreferences userPreferences(UserPreferencesRef ref) {
  final userProfile = ref.watch(userProfileNotifierProvider);
  final preferencesJson = userProfile.valueOrNull?.preferences ?? {};

  try {
    return UserPreferences.fromJson(preferencesJson);
  } catch (e) {
    // Return default preferences if parsing fails
    return const UserPreferences();
  }
}
