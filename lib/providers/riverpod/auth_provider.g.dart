// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isAuthenticatedHash() => r'80812413602ae3f7e027c827b0c0b9c40c5d7161';

/// See also [isAuthenticated].
@ProviderFor(isAuthenticated)
final isAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isAuthenticated,
  name: r'isAuthenticatedProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$isAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$currentUserHash() => r'dbeb30c67913952b2267ab06a34227099e559353';

/// See also [currentUser].
@ProviderFor(currentUser)
final currentUserProvider = AutoDisposeProvider<User?>.internal(
  currentUser,
  name: r'currentUserProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$currentUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserRef = AutoDisposeProviderRef<User?>;
String _$isEnhancedAuthAuthenticatedHash() =>
    r'46d8590fd9a30ef25ef626c73893b4d9ad6fd06e';

/// See also [isEnhancedAuthAuthenticated].
@ProviderFor(isEnhancedAuthAuthenticated)
final isEnhancedAuthAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isEnhancedAuthAuthenticated,
  name: r'isEnhancedAuthAuthenticatedProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$isEnhancedAuthAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsEnhancedAuthAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$enhancedAuthHash() => r'bb4dea6c56f4bb99f41a52e105d085ac52919664';

/// See also [EnhancedAuth].
@ProviderFor(EnhancedAuth)
final enhancedAuthProvider =
    AutoDisposeStreamNotifierProvider<EnhancedAuth, AuthState>.internal(
      EnhancedAuth.new,
      name: r'enhancedAuthProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$enhancedAuthHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$EnhancedAuth = AutoDisposeStreamNotifier<AuthState>;
String _$authHash() => r'01819280906d73bee13c0a86b6467157da9aa2d4';

/// See also [Auth].
@ProviderFor(Auth)
final authProvider =
    AutoDisposeStreamNotifierProvider<Auth, AuthState>.internal(
      Auth.new,
      name: r'authProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product') ? null : _$authHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Auth = AutoDisposeStreamNotifier<AuthState>;
String _$phoneAuthNotifierHash() => r'5f0c55fa7d0fe048113706769035228386542ed3';

/// See also [PhoneAuthNotifier].
@ProviderFor(PhoneAuthNotifier)
final phoneAuthNotifierProvider =
    AutoDisposeNotifierProvider<PhoneAuthNotifier, PhoneAuthState>.internal(
      PhoneAuthNotifier.new,
      name: r'phoneAuthNotifierProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$phoneAuthNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PhoneAuthNotifier = AutoDisposeNotifier<PhoneAuthState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
