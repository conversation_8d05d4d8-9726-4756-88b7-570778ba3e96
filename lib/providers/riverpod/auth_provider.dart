import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase;
import '../../services/enhanced_auth_service.dart';

part 'auth_provider.g.dart';

// Enhanced Authentication Provider with multiple auth methods
@riverpod
class EnhancedAuth extends _$EnhancedAuth {
  AuthenticationService get _authService => AuthenticationService();

  @override
  Stream<AuthState> build() {
    // Listen to both Firebase and Supabase auth changes
    return _supabase.auth.onAuthStateChange;
  }

  final SupabaseClient _supabase = Supabase.instance.client;

  // Email Authentication
  Future<AuthResult> signInWithEmail({required String email, required String password}) async {
    return await _authService.signInWithEmail(email: email, password: password);
  }

  Future<AuthResult> signUpWithEmail({required String email, required String password, String? displayName}) async {
    return await _authService.signUpWithEmail(email: email, password: password, displayName: displayName);
  }

  // Social Authentication
  Future<AuthResult> signInWithGoogle() async {
    return await _authService.signInWithGoogle();
  }

  Future<AuthResult> signInWithApple() async {
    return await _authService.signInWithApple();
  }

  // Phone Authentication
  Future<void> sendPhoneVerificationCode({
    required String phoneNumber,
    required Function(String verificationId, int? resendToken) onCodeSent,
    required Function(firebase.PhoneAuthCredential credential) onAutoVerificationCompleted,
    required Function(String error) onVerificationFailed,
    required Function(String verificationId) onCodeAutoRetrievalTimeout,
  }) async {
    return await _authService.sendPhoneVerificationCode(
      phoneNumber: phoneNumber,
      onCodeSent: onCodeSent,
      onAutoVerificationCompleted: onAutoVerificationCompleted,
      onVerificationFailed: onVerificationFailed,
      onCodeAutoRetrievalTimeout: onCodeAutoRetrievalTimeout,
    );
  }

  Future<AuthResult> verifyPhoneCode({required String verificationId, required String smsCode}) async {
    return await _authService.verifyPhoneCode(verificationId: verificationId, smsCode: smsCode);
  }

  // Biometric Authentication
  Future<bool> isBiometricAvailable() async {
    return await _authService.isBiometricAvailable();
  }

  Future<bool> authenticateWithBiometrics({String localizedReason = 'Please authenticate to access your account'}) async {
    return await _authService.authenticateWithBiometrics(localizedReason: localizedReason);
  }

  // Password Reset
  Future<bool> sendPasswordResetEmail(String email) async {
    return await _authService.sendPasswordResetEmail(email);
  }

  // Sign Out
  Future<void> signOut() async {
    await _authService.signOut();
  }

  // Delete Account
  Future<bool> deleteAccount() async {
    return await _authService.deleteAccount();
  }

  // Getters
  bool get isAuthenticated => _authService.isAuthenticated;
  firebase.User? get currentFirebaseUser => _authService.currentUser;
  User? get currentSupabaseUser => _supabase.auth.currentUser;
}

// Legacy Auth Provider (keeping for backward compatibility)
@riverpod
class Auth extends _$Auth {
  final SupabaseClient _supabase = Supabase.instance.client;

  @override
  Stream<AuthState> build() {
    return _supabase.auth.onAuthStateChange;
  }

  Future<AuthResponse> signUp({required String email, required String password}) async {
    try {
      final response = await _supabase.auth.signUp(email: email, password: password);
      return response;
    } catch (e) {
      throw Exception('Failed to sign up: $e');
    }
  }

  Future<AuthResponse> signIn({required String email, required String password}) async {
    try {
      final response = await _supabase.auth.signInWithPassword(email: email, password: password);
      return response;
    } catch (e) {
      throw Exception('Failed to sign in: $e');
    }
  }

  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }

  bool get isAuthenticated => _supabase.auth.currentUser != null;

  User? get currentUser => _supabase.auth.currentUser;
}

// Phone Authentication State Provider
@riverpod
class PhoneAuthNotifier extends _$PhoneAuthNotifier {
  @override
  PhoneAuthState build() {
    return const PhoneAuthState.initial();
  }

  void setCodeSent(String verificationId, String phoneNumber, int? resendToken) {
    state = PhoneAuthState.codeSent(verificationId: verificationId, phoneNumber: phoneNumber, resendToken: resendToken);
  }

  void setCodeVerified(String phoneNumber) {
    state = PhoneAuthState.codeVerified(phoneNumber: phoneNumber);
  }

  void setError(String message, String? errorCode) {
    state = PhoneAuthState.error(message: message, errorCode: errorCode);
  }

  void setLoading(String? message) {
    state = PhoneAuthState.loading(message: message);
  }

  void reset() {
    state = const PhoneAuthState.initial();
  }
}

// Helper providers for auth states
@riverpod
bool isAuthenticated(IsAuthenticatedRef ref) {
  final authState = ref.watch(authProvider);
  return authState.valueOrNull?.session != null;
}

@riverpod
User? currentUser(CurrentUserRef ref) {
  final authState = ref.watch(authProvider);
  return authState.valueOrNull?.session?.user;
}

@riverpod
bool isEnhancedAuthAuthenticated(IsEnhancedAuthAuthenticatedRef ref) {
  final enhancedAuth = ref.watch(enhancedAuthProvider.notifier);
  return enhancedAuth.isAuthenticated;
}

// Phone Auth State Models (temporary until we can use freezed)
abstract class PhoneAuthState {
  const PhoneAuthState();

  const factory PhoneAuthState.initial() = PhoneAuthInitial;
  const factory PhoneAuthState.codeSent({required String verificationId, required String phoneNumber, int? resendToken}) =
      PhoneAuthCodeSent;
  const factory PhoneAuthState.codeVerified({required String phoneNumber}) = PhoneAuthCodeVerified;
  const factory PhoneAuthState.error({required String message, String? errorCode}) = PhoneAuthError;
  const factory PhoneAuthState.loading({String? message}) = PhoneAuthLoading;
}

class PhoneAuthInitial extends PhoneAuthState {
  const PhoneAuthInitial();
}

class PhoneAuthCodeSent extends PhoneAuthState {
  final String verificationId;
  final String phoneNumber;
  final int? resendToken;

  const PhoneAuthCodeSent({required this.verificationId, required this.phoneNumber, this.resendToken});
}

class PhoneAuthCodeVerified extends PhoneAuthState {
  final String phoneNumber;

  const PhoneAuthCodeVerified({required this.phoneNumber});
}

class PhoneAuthError extends PhoneAuthState {
  final String message;
  final String? errorCode;

  const PhoneAuthError({required this.message, this.errorCode});
}

class PhoneAuthLoading extends PhoneAuthState {
  final String? message;

  const PhoneAuthLoading({this.message});
}
