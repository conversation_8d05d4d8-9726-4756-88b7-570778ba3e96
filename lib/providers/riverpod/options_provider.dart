import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../services/options_service.dart';
import '../../models/option_data.dart';

part 'options_provider.g.dart';

// State class to hold all options data
class OptionsState {
  final List<String> expiryDates;
  final String? selectedExpiryDate;
  final List<OptionData> optionChain;
  final List<OptionData> firstTimestampOptionChain;
  final Map<String, List<OptionData>> optionChainCache;
  final Map<String, List<OptionData>> firstTimestampCache;
  final bool isLoading;
  final String? error;
  final Duration refreshInterval;
  final bool isInitialized;
  final DateTime? lastTimestamp;
  final bool autoRefreshEnabled;

  OptionsState({
    this.expiryDates = const [],
    this.selectedExpiryDate,
    this.optionChain = const [],
    this.firstTimestampOptionChain = const [],
    this.optionChainCache = const {},
    this.firstTimestampCache = const {},
    this.isLoading = false,
    this.error,
    this.refreshInterval = const Duration(minutes: 1),
    this.isInitialized = false,
    this.lastTimestamp,
    this.autoRefreshEnabled = true,
  });

  // Create a new instance with updated fields
  OptionsState copyWith({
    List<String>? expiryDates,
    String? selectedExpiryDate,
    List<OptionData>? optionChain,
    List<OptionData>? firstTimestampOptionChain,
    Map<String, List<OptionData>>? optionChainCache,
    Map<String, List<OptionData>>? firstTimestampCache,
    bool? isLoading,
    String? error,
    Duration? refreshInterval,
    bool? isInitialized,
    DateTime? lastTimestamp,
    bool? autoRefreshEnabled,
  }) {
    return OptionsState(
      expiryDates: expiryDates ?? this.expiryDates,
      selectedExpiryDate: selectedExpiryDate ?? this.selectedExpiryDate,
      optionChain: optionChain ?? this.optionChain,
      firstTimestampOptionChain: firstTimestampOptionChain ?? this.firstTimestampOptionChain,
      optionChainCache: optionChainCache ?? this.optionChainCache,
      firstTimestampCache: firstTimestampCache ?? this.firstTimestampCache,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      refreshInterval: refreshInterval ?? this.refreshInterval,
      isInitialized: isInitialized ?? this.isInitialized,
      lastTimestamp: lastTimestamp ?? this.lastTimestamp,
      autoRefreshEnabled: autoRefreshEnabled ?? this.autoRefreshEnabled,
    );
  }

  // Helper to clear error
  OptionsState clearError() {
    return copyWith(error: null);
  }
}

// Main Options provider
@Riverpod(keepAlive: true)
class Options extends _$Options {
  final OptionsService _service = OptionsService();
  Timer? _refreshTimer;
  StreamSubscription? _optionChainSubscription;

  @override
  OptionsState build() {
    ref.onDispose(() {
      _refreshTimer?.cancel();
      _cancelSubscription();
    });

    // Initialize on first build
    Future.microtask(() => initialize());

    return OptionsState();
  }

  // Helper method to convert decimal IV to percentage for display
  double getIVPercentage(double decimalIV) => decimalIV * 100;

  // Helper method to get ATM strike price
  double getAtmStrike() {
    if (state.optionChain.isEmpty) return 0;

    final spotPrice = state.optionChain.first.spotPrice;
    return state.optionChain.map((e) => e.strikePrice).reduce((a, b) => (a - spotPrice).abs() < (b - spotPrice).abs() ? a : b);
  }

  // Calculate highest volume for a given option type (ce or pe)
  int getHighestVolume(String optionType) {
    if (state.optionChain.isEmpty) return 0;

    final filteredOptions = state.optionChain.where((option) => option.optionType.toLowerCase() == optionType.toLowerCase());
    if (filteredOptions.isEmpty) return 0;

    return filteredOptions.map((e) => e.volume).reduce((a, b) => a > b ? a : b);
  }

  // Get volume change percent from first timestamp data for a specific option
  double getVolumeChangePercent(double strikePrice, String optionType) {
    if (state.optionChain.isEmpty || state.firstTimestampOptionChain.isEmpty) return 0.0;

    try {
      // Find the current option
      final currentOption = state.optionChain.firstWhere(
        (option) => option.strikePrice == strikePrice && option.optionType.toLowerCase() == optionType.toLowerCase(),
        orElse: () => state.optionChain.first, // Fallback
      );

      // Find the first timestamp option
      final firstTimestampOptions =
          state.firstTimestampOptionChain.where((option) => option.optionType.toLowerCase() == optionType.toLowerCase()).toList();

      if (firstTimestampOptions.isEmpty) return 0.0;

      // First try to find matching strike price
      final firstOption = firstTimestampOptions.firstWhere(
        (option) => option.strikePrice == strikePrice,
        orElse: () => firstTimestampOptions.first, // Fallback to first option if strike not found
      );

      // Calculate percentage change
      if (firstOption.volume == 0) return 0.0;
      return (currentOption.volume - firstOption.volume) / firstOption.volume;
    } catch (e) {
      // In case of any error, just return 0.0 as fallback
      return 0.0;
    }
  }

  // Set auto-refresh enabled/disabled
  void setAutoRefresh(bool enabled) {
    state = state.copyWith(autoRefreshEnabled: enabled);
    if (enabled) {
      _setupRefreshTimer();
    } else {
      _refreshTimer?.cancel();
      _refreshTimer = null;
    }
  }

  // Update refresh interval with Duration parameter
  void setRefreshInterval(Duration interval) {
    state = state.copyWith(refreshInterval: interval);
    if (state.autoRefreshEnabled) {
      _setupRefreshTimer();
    }
  }

  Future<void> initialize() async {
    if (state.isInitialized) return;
    await loadExpiryDates();
    if (state.expiryDates.isNotEmpty) {
      await setSelectedExpiryDate(state.expiryDates.first);
    }
    state = state.copyWith(isInitialized: true);
  }

  Future<void> loadExpiryDates() async {
    state = state.copyWith(isLoading: true);
    try {
      final expiryDates = await _service.getExpiryDates();
      state = state.copyWith(expiryDates: expiryDates).clearError();
    } on OptionsServiceException catch (e) {
      state = state.copyWith(error: e.toString(), expiryDates: []);
    } catch (e) {
      state = state.copyWith(error: 'Unexpected error loading expiry dates: $e', expiryDates: []);
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> setSelectedExpiryDate(String date) async {
    if (state.selectedExpiryDate == date) return;

    // Cancel existing subscription before changing date
    await _cancelSubscription();

    state = state.copyWith(selectedExpiryDate: date);

    if (state.optionChainCache.containsKey(date)) {
      state = state.copyWith(optionChain: state.optionChainCache[date]!);
    }

    // Also load first timestamp data if we have it cached
    if (state.firstTimestampCache.containsKey(date)) {
      state = state.copyWith(firstTimestampOptionChain: state.firstTimestampCache[date]!);
    }

    await loadOptionChain();
    // _setupSubscription();

    // Only setup refresh timer if auto-refresh is enabled
    if (state.autoRefreshEnabled) {
      _setupRefreshTimer();
    }
  }

  Future<void> loadOptionChain() async {
    if (state.selectedExpiryDate == null) return;

    state = state.copyWith(isLoading: true);
    try {
      // Get latest option chain data
      final data = await _service.getLatestOptionChain(state.selectedExpiryDate!);

      // Get first timestamp option chain data for the day
      final firstTimestampData = await _service.getFirstTimestampOptionChain(state.selectedExpiryDate!);

      if (data.isNotEmpty) {
        _updateOptionChain(data, firstTimestampData);
        try {
          final timestamp = DateTime.tryParse(data.first.timestamp);
          state = state.copyWith(lastTimestamp: timestamp);
        } catch (e) {
          // Handle datetime parsing error gracefully
          state = state.copyWith(lastTimestamp: DateTime.now());
        }
      }
      state = state.clearError();
    } on OptionsServiceException catch (e) {
      state = state.copyWith(error: e.toString());
    } catch (e) {
      state = state.copyWith(error: 'Unexpected error loading option chain: $e');
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  void _updateOptionChain(List<OptionData> data, List<OptionData> firstTimestampData) {
    if (data.isEmpty) return;

    // Update the latest option chain
    final newCache = Map<String, List<OptionData>>.from(state.optionChainCache);
    if (state.selectedExpiryDate != null) {
      newCache[state.selectedExpiryDate!] = data;
    }

    // Update the first timestamp option chain
    final newFirstTimestampCache = Map<String, List<OptionData>>.from(state.firstTimestampCache);
    if (state.selectedExpiryDate != null) {
      newFirstTimestampCache[state.selectedExpiryDate!] = firstTimestampData;
    }

    state = state.copyWith(
      optionChain: data,
      optionChainCache: newCache,
      firstTimestampOptionChain: firstTimestampData,
      firstTimestampCache: newFirstTimestampCache,
    );
  }

  // Safely cancel existing subscription
  Future<void> _cancelSubscription() async {
    if (_optionChainSubscription != null) {
      try {
        await _optionChainSubscription!.cancel();
        _optionChainSubscription = null;
      } catch (e) {
        print('Error cancelling subscription: $e');
        // Set to null even if cancel failed
        _optionChainSubscription = null;
      }
    }
  }

  // void _setupSubscription() {
  //   if (state.selectedExpiryDate == null) return;

  //   // Ensure any existing subscription is cancelled
  //   _cancelSubscription().then((_) {
  //     try {
  //       _optionChainSubscription = _service
  //           .subscribeToOptionChain(state.selectedExpiryDate!)
  //           .listen(
  //             (data) {
  //               if (data.isNotEmpty) {
  //                 _updateOptionChain(data);
  //               }
  //             },
  //             onError: (error) {
  //               state = state.copyWith(
  //                 error: error is OptionsServiceException ? error.toString() : 'Error in option chain subscription: $error',
  //               );

  //               // Auto-reconnect on error after a delay
  //               Future.delayed(const Duration(seconds: 5), () {
  //                 if (_optionChainSubscription != null) {
  //                   _setupSubscription();
  //                 }
  //               });
  //             },
  //             cancelOnError: true,
  //           );
  //     } catch (e) {
  //       state = state.copyWith(error: 'Failed to set up subscription: $e');
  //     }
  //   });
  // }

  void _setupRefreshTimer() {
    _refreshTimer?.cancel();
    // Only set up timer if auto-refresh is enabled
    if (state.autoRefreshEnabled) {
      _refreshTimer = Timer.periodic(state.refreshInterval, (_) => refresh());
    }
  }

  void refresh() {
    // Cancel and restart subscription on refresh
    _cancelSubscription().then((_) {
      loadOptionChain();
    });
  }
}

// Helper providers for common option chain operations
@riverpod
double atmStrike(ref) {
  final optionsState = ref.watch(optionsProvider);
  if (optionsState.optionChain.isEmpty) return 0;

  final spotPrice = optionsState.optionChain.first.spotPrice;
  return optionsState.optionChain
      .map((e) => e.strikePrice)
      .reduce((a, b) => (a - spotPrice).abs() < (b - spotPrice).abs() ? a : b);
}

@riverpod
bool isOptionsLoading(ref) {
  return ref.watch(optionsProvider).isLoading;
}

@riverpod
List<OptionData> filteredOptionChain(ref, {double? minStrike, double? maxStrike}) {
  final optionChain = ref.watch(optionsProvider).optionChain;

  if (minStrike == null && maxStrike == null) {
    return optionChain;
  }

  return optionChain.where((option) {
    if (minStrike != null && option.strikePrice < minStrike) return false;
    if (maxStrike != null && option.strikePrice > maxStrike) return false;
    return true;
  }).toList();
}
