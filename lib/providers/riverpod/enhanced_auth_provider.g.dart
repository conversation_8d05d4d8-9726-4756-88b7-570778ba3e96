// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enhanced_auth_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isEnhancedAuthenticatedHash() =>
    r'673a11d7dbb4d281beac8a8fcaadc2b9fee656ef';

/// See also [isEnhancedAuthenticated].
@ProviderFor(isEnhancedAuthenticated)
final isEnhancedAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isEnhancedAuthenticated,
  name: r'isEnhancedAuthenticatedProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$isEnhancedAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsEnhancedAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$currentFirebaseUserHash() =>
    r'22c0f7e220e18be61cd2e125c8f7c51681ea668f';

/// See also [currentFirebaseUser].
@ProviderFor(currentFirebaseUser)
final currentFirebaseUserProvider =
    AutoDisposeProvider<firebase.User?>.internal(
      currentFirebaseUser,
      name: r'currentFirebaseUserProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$currentFirebaseUserHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentFirebaseUserRef = AutoDisposeProviderRef<firebase.User?>;
String _$enhancedAuthNotifierHash() =>
    r'3b63c6a27b43c12af554e8cdcbbe5da3002ca77a';

/// See also [EnhancedAuthNotifier].
@ProviderFor(EnhancedAuthNotifier)
final enhancedAuthNotifierProvider = AutoDisposeNotifierProvider<
  EnhancedAuthNotifier,
  AuthenticationState
>.internal(
  EnhancedAuthNotifier.new,
  name: r'enhancedAuthNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$enhancedAuthNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EnhancedAuthNotifier = AutoDisposeNotifier<AuthenticationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
