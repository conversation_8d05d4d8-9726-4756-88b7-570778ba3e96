// import 'dart:async';
// import 'package:flutter/foundation.dart';
// import '../services/options_service.dart';
// import '../models/option_data.dart';

// class OptionsProvider with ChangeNotifier {
//   final OptionsService _service;
//   Timer? _refreshTimer;
//   StreamSubscription? _optionChainSubscription;

//   List<String> _expiryDates = [];
//   String? _selectedExpiryDate;
//   List<OptionData> _optionChain = [];
//   Map<String, List<OptionData>> _optionChainCache = {};
//   bool _isLoading = false;
//   String? _error;
//   Duration _refreshInterval = const Duration(minutes: 1);
//   bool _isInitialized = false;
//   DateTime? _lastTimestamp;
//   bool _autoRefreshEnabled = true; // Flag to control auto-refresh

//   OptionsProvider({OptionsService? service}) : _service = service ?? OptionsService();

//   List<String> get expiryDates => _expiryDates;
//   String? get selectedExpiryDate => _selectedExpiryDate;
//   List<OptionData> get optionChain => _optionChain;
//   bool get isLoading => _isLoading;
//   String? get error => _error;
//   Duration get refreshInterval => _refreshInterval;
//   bool get isInitialized => _isInitialized;
//   DateTime? get lastTimestamp => _lastTimestamp;
//   bool get autoRefreshEnabled => _autoRefreshEnabled;

//   // Helper method to convert decimal IV to percentage for display
//   double getIVPercentage(double decimalIV) => decimalIV * 100;

//   // Helper method to get ATM strike price
//   double getAtmStrike() {
//     if (_optionChain.isEmpty) return 0;

//     final spotPrice = _optionChain.first.spotPrice;
//     return _optionChain.map((e) => e.strikePrice).reduce((a, b) => (a - spotPrice).abs() < (b - spotPrice).abs() ? a : b);
//   }

//   // Set auto-refresh enabled/disabled
//   void setAutoRefresh(bool enabled) {
//     _autoRefreshEnabled = enabled;
//     if (enabled) {
//       _setupRefreshTimer();
//     } else {
//       _refreshTimer?.cancel();
//       _refreshTimer = null;
//     }
//     notifyListeners();
//   }

//   void setRefreshInterval(Duration interval) {
//     _refreshInterval = interval;
//     if (_autoRefreshEnabled) {
//       _setupRefreshTimer();
//     }
//   }

//   Future<void> initialize() async {
//     if (_isInitialized) return;
//     await loadExpiryDates();
//     if (_expiryDates.isNotEmpty) {
//       await setSelectedExpiryDate(_expiryDates.first);
//     }
//     _isInitialized = true;
//   }

//   Future<void> loadExpiryDates() async {
//     _setLoading(true);
//     try {
//       _expiryDates = await _service.getExpiryDates();
//       _error = null;
//     } on OptionsServiceException catch (e) {
//       _error = e.toString();
//       _expiryDates = [];
//     } catch (e) {
//       _error = 'Unexpected error loading expiry dates: $e';
//       _expiryDates = [];
//     } finally {
//       _setLoading(false);
//     }
//   }

//   Future<void> setSelectedExpiryDate(String date) async {
//     if (_selectedExpiryDate == date) return;

//     // Cancel existing subscription before changing date
//     await _cancelSubscription();

//     _selectedExpiryDate = date;

//     if (_optionChainCache.containsKey(date)) {
//       _optionChain = _optionChainCache[date]!;
//       notifyListeners();
//     }

//     await loadOptionChain();
//     _setupSubscription();

//     // Only setup refresh timer if auto-refresh is enabled
//     if (_autoRefreshEnabled) {
//       _setupRefreshTimer();
//     }
//   }

//   Future<void> loadOptionChain() async {
//     if (_selectedExpiryDate == null) return;

//     _setLoading(true);
//     try {
//       final data = await _service.getLatestOptionChain(_selectedExpiryDate!);
//       if (data.isNotEmpty) {
//         _updateOptionChain(data);
//         try {
//           _lastTimestamp = DateTime.tryParse(data.first.timestamp);
//         } catch (e) {
//           // Handle datetime parsing error gracefully
//           _lastTimestamp = DateTime.now();
//         }
//       }
//       _error = null;
//     } on OptionsServiceException catch (e) {
//       _error = e.toString();
//     } catch (e) {
//       _error = 'Unexpected error loading option chain: $e';
//     } finally {
//       _setLoading(false);
//     }
//   }

//   void _updateOptionChain(List<OptionData> data) {
//     if (data.isEmpty) return;

//     _optionChain = data;
//     if (_selectedExpiryDate != null) {
//       _optionChainCache[_selectedExpiryDate!] = data;
//     }
//     notifyListeners();
//   }

//   // Safely cancel existing subscription
//   Future<void> _cancelSubscription() async {
//     if (_optionChainSubscription != null) {
//       try {
//         await _optionChainSubscription!.cancel();
//         _optionChainSubscription = null;
//       } catch (e) {
//         print('Error cancelling subscription: $e');
//         // Set to null even if cancel failed
//         _optionChainSubscription = null;
//       }
//     }
//   }

//   void _setupSubscription() {
//     if (_selectedExpiryDate == null) return;

//     // Ensure any existing subscription is cancelled
//     _cancelSubscription().then((_) {
//       try {
//         _optionChainSubscription = _service
//             .subscribeToOptionChain(_selectedExpiryDate!)
//             .listen(
//               (data) {
//                 if (data.isNotEmpty) {
//                   _updateOptionChain(data);
//                 }
//               },
//               onError: (error) {
//                 _error = error is OptionsServiceException ? error.toString() : 'Error in option chain subscription: $error';
//                 notifyListeners();

//                 // Auto-reconnect on error after a delay
//                 Future.delayed(const Duration(seconds: 5), () {
//                   if (_optionChainSubscription != null) {
//                     _setupSubscription();
//                   }
//                 });
//               },
//               cancelOnError: true,
//             );
//       } catch (e) {
//         _error = 'Failed to set up subscription: $e';
//         notifyListeners();
//       }
//     });
//   }

//   void _setupRefreshTimer() {
//     _refreshTimer?.cancel();
//     // Only set up timer if auto-refresh is enabled
//     if (_autoRefreshEnabled) {
//       _refreshTimer = Timer.periodic(_refreshInterval, (_) => refresh());
//     }
//   }

//   void _setLoading(bool value) {
//     _isLoading = value;
//     notifyListeners();
//   }

//   void refresh() {
//     // Cancel and restart subscription on refresh
//     _cancelSubscription().then((_) {
//       loadOptionChain().then((_) {
//         _setupSubscription();
//       });
//     });
//   }

//   @override
//   void dispose() {
//     _refreshTimer?.cancel();
//     _cancelSubscription();
//     super.dispose();
//   }
// }
