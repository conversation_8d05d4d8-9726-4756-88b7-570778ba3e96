import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/logarte_config.dart';

/// Extension to add Logarte logging to Supabase queries
extension SupabaseLogging on SupabaseQueryBuilder {
  /// Enhanced select with logging
  PostgrestFilterBuilder<PostgrestList> selectWithLogging(String columns, {String? tableName}) {
    logarte.log('📊 Supabase Query: SELECT $columns FROM ${tableName ?? 'table'}');
    return select(columns);
  }
}

/// Helper class to log Supabase operations with Logarte
class LoggedSupabaseClient {
  final SupabaseClient _client;

  LoggedSupabaseClient(this._client);

  /// Log a database query operation
  void logQuery({required String table, required String operation, Map<String, dynamic>? filters, String? orderBy, int? limit}) {
    final filterStr = filters?.entries.map((e) => '${e.key}=${e.value}').join(', ') ?? '';

    final queryDetails = [
      'Table: $table',
      'Operation: $operation',
      if (filterStr.isNotEmpty) 'Filters: $filterStr',
      if (orderBy != null) 'Order: $orderBy',
      if (limit != null) 'Limit: $limit',
    ].join(' | ');

    logarte.database(target: table, value: queryDetails, source: 'Supabase');
  }

  /// Log a successful response
  void logSuccess({required String operation, required String table, int? recordCount, Duration? duration}) {
    final message = [
      '✅ $operation successful',
      'Table: $table',
      if (recordCount != null) 'Records: $recordCount',
      if (duration != null) 'Duration: ${duration.inMilliseconds}ms',
    ].join(' | ');

    logarte.log(message);
  }

  /// Log an error
  void logError({required String operation, required String table, required dynamic error, Duration? duration}) {
    final message = [
      '❌ $operation failed',
      'Table: $table',
      'Error: $error',
      if (duration != null) 'Duration: ${duration.inMilliseconds}ms',
    ].join(' | ');

    logarte.log(message);
  }

  /// Get the underlying Supabase client
  SupabaseClient get client => _client;

  /// Proxied from method
  SupabaseQueryBuilder from(String table) {
    return _client.from(table);
  }
}

/// Extension to easily get a logged Supabase client
extension SupabaseClientLogging on SupabaseClient {
  LoggedSupabaseClient get logged => LoggedSupabaseClient(this);
}
