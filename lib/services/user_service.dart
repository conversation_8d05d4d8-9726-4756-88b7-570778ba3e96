import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_profile_simple.dart';

class UserService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get current user profile
  Future<UserProfile?> getCurrentUserProfile() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return null;

      final response = await _supabase.from('user_profiles').select().eq('id', user.id).maybeSingle();

      if (response == null) {
        // Create a new profile if it doesn't exist
        return await createUserProfile(user);
      }

      return UserProfile.fromJson(response);
    } catch (e) {
      throw Exception('Failed to get user profile: $e');
    }
  }

  // Create user profile
  Future<UserProfile> createUserProfile(User user) async {
    try {
      final profile = UserProfile(
        id: user.id,
        email: user.email ?? '',
        fullName: user.userMetadata?['full_name'],
        avatarUrl: user.userMetadata?['avatar_url'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final response = await _supabase.from('user_profiles').insert(profile.toJson()).select().single();

      return UserProfile.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create user profile: $e');
    }
  }

  // Update user profile
  Future<UserProfile> updateUserProfile(UserProfile profile) async {
    try {
      final updatedProfile = profile.copyWith(updatedAt: DateTime.now());

      final response =
          await _supabase.from('user_profiles').update(updatedProfile.toJson()).eq('id', profile.id).select().single();

      return UserProfile.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update user profile: $e');
    }
  }

  // Update onboarding status
  Future<UserProfile> completeOnboarding(String userId) async {
    try {
      final response =
          await _supabase
              .from('user_profiles')
              .update({'is_onboarded': true, 'updated_at': DateTime.now().toIso8601String()})
              .eq('id', userId)
              .select()
              .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      throw Exception('Failed to complete onboarding: $e');
    }
  }

  // Update user preferences
  Future<UserProfile> updatePreferences(String userId, UserPreferences preferences) async {
    try {
      final response =
          await _supabase
              .from('user_profiles')
              .update({'preferences': preferences.toJson(), 'updated_at': DateTime.now().toIso8601String()})
              .eq('id', userId)
              .select()
              .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update preferences: $e');
    }
  }

  // Get onboarding steps
  List<OnboardingStep> getOnboardingSteps() {
    return [
      const OnboardingStep(
        id: 'welcome',
        title: 'Welcome to Options AI',
        description: 'Your intelligent trading companion for options analytics and insights.',
        icon: 'welcome',
      ),
      const OnboardingStep(
        id: 'profile',
        title: 'Complete Your Profile',
        description: 'Tell us about yourself to personalize your trading experience.',
        icon: 'profile',
      ),
      const OnboardingStep(
        id: 'preferences',
        title: 'Set Your Preferences',
        description: 'Configure your trading preferences and notification settings.',
        icon: 'settings',
      ),
      const OnboardingStep(
        id: 'market_selection',
        title: 'Choose Your Markets',
        description: 'Select the markets and instruments you want to trade.',
        icon: 'market',
      ),
      const OnboardingStep(
        id: 'ai_setup',
        title: 'AI Assistant Setup',
        description: 'Configure your AI assistant preferences and risk tolerance.',
        icon: 'ai',
      ),
    ];
  }

  // Delete user profile (for account deletion)
  Future<void> deleteUserProfile(String userId) async {
    try {
      await _supabase.from('user_profiles').delete().eq('id', userId);
    } catch (e) {
      throw Exception('Failed to delete user profile: $e');
    }
  }
}
