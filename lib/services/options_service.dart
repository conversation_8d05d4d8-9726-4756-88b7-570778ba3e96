import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/option_data.dart';
import '../config/logarte_config.dart';
import '../services/logged_supabase.dart';
import 'dart:async';

class OptionsServiceException implements Exception {
  final String message;
  final dynamic originalError;

  OptionsServiceException(this.message, [this.originalError]);

  @override
  String toString() => 'OptionsServiceException: $message${originalError != null ? '\nOriginal error: $originalError' : ''}';
}

class OptionsService {
  final SupabaseClient _supabase = Supabase.instance.client;
  final LoggedSupabaseClient _loggedSupabase = Supabase.instance.client.logged;
  final _retryDuration = const Duration(seconds: 5);
  final _maxRetries = 3;

  Future<T> _withRetry<T>(Future<T> Function() operation, String errorMessage) async {
    int attempts = 0;
    final stopwatch = Stopwatch()..start();

    logarte.log('🔄 Starting operation: $errorMessage');

    while (attempts < _maxRetries) {
      try {
        final result = await operation();
        stopwatch.stop();
        logarte.log('✅ Operation completed: $errorMessage (${stopwatch.elapsedMilliseconds}ms, attempts: ${attempts + 1})');
        return result;
      } catch (e) {
        attempts++;
        logarte.log('⚠️ Operation failed (attempt $attempts): $errorMessage - $e');
        if (attempts == _maxRetries) {
          stopwatch.stop();
          logarte.log(
            '❌ Operation failed permanently: $errorMessage after $_maxRetries attempts (${stopwatch.elapsedMilliseconds}ms)',
          );
          throw OptionsServiceException('$errorMessage after $_maxRetries attempts', e);
        }
        await Future.delayed(_retryDuration);
      }
    }
    throw OptionsServiceException('Unexpected error in retry loop');
  }

  Future<List<String>> getExpiryDates() async {
    return _withRetry(() async {
      logarte.database(target: 'expiry_dates', value: 'SELECT expiry_date ORDER BY expiry_date', source: 'Supabase');

      final List<dynamic> response = await _supabase.from('expiry_dates').select('expiry_date').order('expiry_date');

      if (response.isEmpty) {
        throw OptionsServiceException('No expiry dates found');
      }

      final Set<String> uniqueDates =
          response.map((row) => row['expiry_date'].toString()).where((date) => date.isNotEmpty).toSet();

      logarte.log('📅 Retrieved ${uniqueDates.length} expiry dates');
      return uniqueDates.toList()..sort();
    }, 'Failed to fetch expiry dates');
  }

  Future<List<OptionData>> getLatestOptionChain(String dateStr) async {
    return _withRetry(() async {
      // Get the latest timestamp for the given date
      final latestTimestampResponse =
          await _supabase
              .from('nifty')
              .select('timestamp')
              .eq('expiry_date', dateStr)
              .order('timestamp', ascending: false)
              .limit(1)
              .single();

      final latestTimestamp = latestTimestampResponse['timestamp'];

      // Get all records for the latest timestamp
      final List<dynamic> response = await _supabase
          .from('nifty')
          .select()
          .eq('timestamp', latestTimestamp)
          .order('strike_price');

      if (response.isEmpty) {
        throw OptionsServiceException('No options data found for the selected date');
      }

      return response.map((row) => OptionData.fromMap(row)).toList();
    }, 'Failed to fetch latest option chain');
  }

  // Get the first timestamp option chain for a given day (morning/market open data)
  Future<List<OptionData>> getFirstTimestampOptionChain(String dateStr) async {
    return _withRetry(() async {
      // Get the first timestamp for the given date
      final firstTimestampResponse =
          await _supabase
              .from('nifty')
              .select('timestamp')
              .eq('expiry_date', dateStr)
              .order('timestamp', ascending: true)
              .limit(1)
              .single();

      final firstTimestamp = firstTimestampResponse['timestamp'];

      // Get all records for the first timestamp
      final List<dynamic> response = await _supabase.from('nifty').select().eq('timestamp', firstTimestamp).order('strike_price');

      if (response.isEmpty) {
        throw OptionsServiceException('No options data found for the selected date');
      }

      return response.map((row) => OptionData.fromMap(row)).toList();
    }, 'Failed to fetch first timestamp option chain');
  }

  Future<List<OptionData>> getHistoricalData(String dateStr, {DateTime? startTime, DateTime? endTime}) async {
    return _withRetry(() async {
      var query = _supabase.from('nifty_timeseries').select().eq('expiry_date', dateStr);
      final List<dynamic> response = await query.order('timestamp', ascending: true);

      if (response.isEmpty) {
        throw OptionsServiceException('No historical data found for the selected date');
      }

      // Filter the response in memory based on time range
      final filteredResponse =
          response.where((row) {
            final rowTime = DateTime.tryParse(row['timestamp']?.toString() ?? '');
            if (rowTime == null) return false;

            bool withinRange = true;

            if (startTime != null) {
              withinRange = withinRange && rowTime.isAfter(startTime);
            }
            if (endTime != null) {
              withinRange = withinRange && rowTime.isBefore(endTime);
            }

            return withinRange;
          }).toList();

      return filteredResponse.map((row) => OptionData.fromMap(row)).toList();
    }, 'Failed to fetch historical data');
  }

  // Get OHLCV data for a specific option
  Future<List<OHLCVData>> getOHLCVData({
    required String dateStr,
    required double strikePrice,
    required String optionType,
    String interval = '5 minutes',
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    return _withRetry(() async {
      var query = _supabase
          .from('nifty_ohlcv')
          .select()
          .eq('expiry_date', dateStr)
          .eq('strike_price', strikePrice)
          .eq('option_type', optionType)
          .order('time_bucket', ascending: true);

      final response = await query;

      if (response.isEmpty) {
        throw OptionsServiceException('No OHLCV data available for the selected parameters');
      }

      // Filter the response in memory based on time range
      final filteredResponse =
          (response as List).where((row) {
            final rowTime = DateTime.parse(row['time_bucket']);
            bool withinRange = true;

            if (startTime != null) {
              withinRange = withinRange && rowTime.isAfter(startTime);
            }
            if (endTime != null) {
              withinRange = withinRange && rowTime.isBefore(endTime);
            }
            return withinRange;
          }).toList();

      return filteredResponse.map((json) => OHLCVData.fromJson(json)).toList();
    }, 'Failed to fetch OHLCV data');
  }

  // Stream<List<OptionData>> subscribeToOptionChain(String expiryDate) {
  //   // Create a new controller for this subscription
  //   final controller = StreamController<List<OptionData>>();
  //   RealtimeChannel? channel;
  //   Timer? reconnectTimer;

  //   // Function to handle connection and subscription
  //   void connect() {
  //     try {
  //       // Cancel any existing channel first
  //       if (channel != null) {
  //         try {
  //           channel!.unsubscribe();
  //         } catch (e) {
  //           // Ignore error when unsubscribing from already closed channel
  //           channel = null;
  //         }
  //       }

  //       // Create a new channel with a unique name to avoid conflicts
  //       final channelName = 'optionchain-${expiryDate}-${DateTime.now().millisecondsSinceEpoch}';
  //       channel = _supabase.channel(channelName);

  //       // Configure channel to listen to the nifty table filtered by expiry_date
  //       channel = channel!.subscribe()
  //           .on(
  //             'postgres_changes',
  //             {'event': 'INSERT', 'schema': 'public', 'table': 'nifty', 'filter': 'expiry_date=eq.$expiryDate'},
  //             (payload, [ref]) {
  //               _fetchAndUpdateData(controller, expiryDate);
  //             },
  //           )
  //           .on(
  //             'postgres_changes',
  //             {'event': 'UPDATE', 'schema': 'public', 'table': 'nifty', 'filter': 'expiry_date=eq.$expiryDate'},
  //             (payload, [ref]) {
  //               _fetchAndUpdateData(controller, expiryDate);
  //             },
  //           );

  //       // Subscribe to the channel
  //       channel!.subscribe((status, [error]) {
  //         if (error != null) {
  //           if (!controller.isClosed) {
  //             controller.addError(OptionsServiceException('Realtime subscription error', error));
  //           }

  //           // Try to reconnect after delay
  //           reconnectTimer?.cancel();
  //           reconnectTimer = Timer(_retryDuration, connect);
  //         } else {
  //           // Fetch initial data when connected
  //           if (status == 'SUBSCRIBED') {
  //             _fetchAndUpdateData(controller, expiryDate);
  //           }
  //         }
  //       });
  //     } catch (e) {
  //       if (!controller.isClosed) {
  //         controller.addError(OptionsServiceException('Error setting up realtime subscription', e));
  //       }

  //       // Try to reconnect after delay
  //       reconnectTimer?.cancel();
  //       reconnectTimer = Timer(_retryDuration, connect);
  //     }
  //   }

  //   // Set up the controller with cleanup logic
  //   controller.onListen = connect;
  //   controller.onCancel = () {
  //     try {
  //       reconnectTimer?.cancel();
  //       if (channel != null) {
  //         try {
  //           channel!.unsubscribe();
  //         } catch (e) {
  //           // Ignore error when unsubscribing from already closed channel
  //         }
  //         channel = null;
  //       }
  //     } catch (e) {
  //       // Ignore errors during cleanup
  //     }
  //   };

  //   return controller.stream;
  // }

  // Helper method to fetch and update data through the stream controller
  void _fetchAndUpdateData(StreamController<List<OptionData>> controller, String expiryDate) {
    if (controller.isClosed) return;

    try {
      getLatestOptionChain(expiryDate)
          .then((data) {
            if (!controller.isClosed && data.isNotEmpty) {
              controller.add(data);
            }
          })
          .catchError((e) {
            if (!controller.isClosed) {
              controller.addError(OptionsServiceException('Error fetching updated data', e));
            }
          });
    } catch (e) {
      if (!controller.isClosed) {
        controller.addError(OptionsServiceException('Error handling change payload', e));
      }
    }
  }
}

class OHLCVData {
  final DateTime timestamp;
  final double open;
  final double high;
  final double low;
  final double close;
  final int volume;
  final int openInterest;

  OHLCVData({
    required this.timestamp,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
    required this.openInterest,
  });

  factory OHLCVData.fromJson(Map<String, dynamic> json) {
    return OHLCVData(
      timestamp: DateTime.parse(json['timestamp']),
      open: (json['open'] as num).toDouble(),
      high: (json['high'] as num).toDouble(),
      low: (json['low'] as num).toDouble(),
      close: (json['close'] as num).toDouble(),
      volume: json['volume'] as int,
      openInterest: json['open_interest'] as int,
    );
  }
}
