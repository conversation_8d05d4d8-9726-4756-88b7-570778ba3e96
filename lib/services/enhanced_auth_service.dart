import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:local_auth/local_auth.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:io';

class AuthenticationService {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final LocalAuthentication _localAuth = LocalAuthentication();
  final supabase.SupabaseClient _supabase = supabase.Supabase.instance.client;

  // Current user getter
  User? get currentUser => _firebaseAuth.currentUser;
  bool get isAuthenticated => currentUser != null;

  // Auth state stream
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  // Email Authentication
  Future<AuthResult> signInWithEmail({required String email, required String password}) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(email: email, password: password);

      if (credential.user != null) {
        // Sync with Supabase
        await _syncUserWithSupabase(credential.user!);

        return AuthResult.success(
          userId: credential.user!.uid,
          email: credential.user!.email!,
          provider: AuthProvider.email,
          isNewUser: false,
        );
      } else {
        return const AuthResult.error(message: 'Authentication failed', provider: AuthProvider.email);
      }
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(message: _getErrorMessage(e.code), errorCode: e.code, provider: AuthProvider.email);
    } catch (e) {
      return AuthResult.error(message: 'An unexpected error occurred', provider: AuthProvider.email);
    }
  }

  Future<AuthResult> signUpWithEmail({required String email, required String password, String? displayName}) async {
    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(email: email, password: password);

      if (credential.user != null) {
        // Update display name if provided
        if (displayName != null && displayName.isNotEmpty) {
          await credential.user!.updateDisplayName(displayName);
        }

        // Send email verification
        await credential.user!.sendEmailVerification();

        // Sync with Supabase
        await _syncUserWithSupabase(credential.user!);

        return AuthResult.success(
          userId: credential.user!.uid,
          email: credential.user!.email!,
          provider: AuthProvider.email,
          isNewUser: true,
        );
      } else {
        return const AuthResult.error(message: 'Account creation failed', provider: AuthProvider.email);
      }
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(message: _getErrorMessage(e.code), errorCode: e.code, provider: AuthProvider.email);
    } catch (e) {
      return AuthResult.error(message: 'An unexpected error occurred', provider: AuthProvider.email);
    }
  }

  // Google Authentication
  Future<AuthResult> signInWithGoogle() async {
    try {
      // Check if running on web
      if (kIsWeb) {
        final GoogleAuthProvider googleProvider = GoogleAuthProvider();
        final credential = await _firebaseAuth.signInWithPopup(googleProvider);

        if (credential.user != null) {
          await _syncUserWithSupabase(credential.user!);
          return AuthResult.success(
            userId: credential.user!.uid,
            email: credential.user!.email!,
            provider: AuthProvider.google,
            isNewUser: credential.additionalUserInfo?.isNewUser ?? false,
          );
        }
      } else {
        // Mobile implementation
        final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

        if (googleUser == null) {
          return const AuthResult.error(message: 'Google sign-in was cancelled', provider: AuthProvider.google);
        }

        final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

        final credential = GoogleAuthProvider.credential(accessToken: googleAuth.accessToken, idToken: googleAuth.idToken);

        final userCredential = await _firebaseAuth.signInWithCredential(credential);

        if (userCredential.user != null) {
          await _syncUserWithSupabase(userCredential.user!);
          return AuthResult.success(
            userId: userCredential.user!.uid,
            email: userCredential.user!.email!,
            provider: AuthProvider.google,
            isNewUser: userCredential.additionalUserInfo?.isNewUser ?? false,
          );
        }
      }

      return const AuthResult.error(message: 'Google authentication failed', provider: AuthProvider.google);
    } catch (e) {
      return AuthResult.error(message: 'Google sign-in failed: ${e.toString()}', provider: AuthProvider.google);
    }
  }

  // Apple Authentication
  Future<AuthResult> signInWithApple() async {
    try {
      // Check if Apple Sign In is available
      if (!Platform.isIOS && !Platform.isMacOS && !kIsWeb) {
        return const AuthResult.error(message: 'Apple Sign In is not available on this platform', provider: AuthProvider.apple);
      }

      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [AppleIDAuthorizationScopes.email, AppleIDAuthorizationScopes.fullName],
        webAuthenticationOptions: kIsWeb
            ? WebAuthenticationOptions(
                clientId: 'your-web-client-id', // Configure this
                redirectUri: Uri.parse('https://your-domain.com/auth/callback'), // Configure this
              )
            : null,
      );

      final oauthCredential = OAuthProvider(
        "apple.com",
      ).credential(idToken: appleCredential.identityToken, accessToken: appleCredential.authorizationCode);

      final userCredential = await _firebaseAuth.signInWithCredential(oauthCredential);

      if (userCredential.user != null) {
        // Update display name if provided by Apple
        if (appleCredential.givenName != null || appleCredential.familyName != null) {
          final displayName = '${appleCredential.givenName ?? ''} ${appleCredential.familyName ?? ''}'.trim();
          if (displayName.isNotEmpty) {
            await userCredential.user!.updateDisplayName(displayName);
          }
        }

        await _syncUserWithSupabase(userCredential.user!);
        return AuthResult.success(
          userId: userCredential.user!.uid,
          email: userCredential.user!.email ?? appleCredential.email ?? '',
          provider: AuthProvider.apple,
          isNewUser: userCredential.additionalUserInfo?.isNewUser ?? false,
        );
      }

      return const AuthResult.error(message: 'Apple authentication failed', provider: AuthProvider.apple);
    } catch (e) {
      return AuthResult.error(message: 'Apple sign-in failed: ${e.toString()}', provider: AuthProvider.apple);
    }
  }

  // Phone Authentication
  Future<void> sendPhoneVerificationCode({
    required String phoneNumber,
    required Function(String verificationId, int? resendToken) onCodeSent,
    required Function(PhoneAuthCredential credential) onAutoVerificationCompleted,
    required Function(String error) onVerificationFailed,
    required Function(String verificationId) onCodeAutoRetrievalTimeout,
  }) async {
    try {
      await _firebaseAuth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: onAutoVerificationCompleted,
        verificationFailed: (FirebaseAuthException e) {
          onVerificationFailed(_getErrorMessage(e.code));
        },
        codeSent: onCodeSent,
        codeAutoRetrievalTimeout: onCodeAutoRetrievalTimeout,
        timeout: const Duration(seconds: 60),
      );
    } catch (e) {
      onVerificationFailed('Failed to send verification code: ${e.toString()}');
    }
  }

  Future<AuthResult> verifyPhoneCode({required String verificationId, required String smsCode}) async {
    try {
      final credential = PhoneAuthProvider.credential(verificationId: verificationId, smsCode: smsCode);

      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        await _syncUserWithSupabase(userCredential.user!);
        return AuthResult.success(
          userId: userCredential.user!.uid,
          email: userCredential.user!.email ?? '',
          provider: AuthProvider.phone,
          isNewUser: userCredential.additionalUserInfo?.isNewUser ?? false,
        );
      }

      return const AuthResult.error(message: 'Phone verification failed', provider: AuthProvider.phone);
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(message: _getErrorMessage(e.code), errorCode: e.code, provider: AuthProvider.phone);
    } catch (e) {
      return AuthResult.error(message: 'Phone verification failed: ${e.toString()}', provider: AuthProvider.phone);
    }
  }

  // Biometric Authentication
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      return false;
    }
  }

  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      return availableBiometrics;
    } catch (e) {
      return [];
    }
  }

  Future<bool> authenticateWithBiometrics({String localizedReason = 'Please authenticate to access your account'}) async {
    try {
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: localizedReason,
        options: const AuthenticationOptions(biometricOnly: true, stickyAuth: true),
      );
      return isAuthenticated;
    } on PlatformException catch (e) {
      debugPrint('Biometric authentication error: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('Biometric authentication error: $e');
      return false;
    }
  }

  // Password Reset
  Future<bool> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Sign Out
  Future<void> signOut() async {
    try {
      await Future.wait([_firebaseAuth.signOut(), _googleSignIn.signOut(), _supabase.auth.signOut()]);
    } catch (e) {
      debugPrint('Error during sign out: $e');
    }
  }

  // Delete Account
  Future<bool> deleteAccount() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        await user.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting account: $e');
      return false;
    }
  }

  // Helper Methods
  Future<void> _syncUserWithSupabase(User firebaseUser) async {
    try {
      // Create or update user profile in Supabase
      final userData = {
        'id': firebaseUser.uid,
        'email': firebaseUser.email,
        'full_name': firebaseUser.displayName,
        'avatar_url': firebaseUser.photoURL,
        'phone': firebaseUser.phoneNumber,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase.from('user_profiles').upsert(userData).eq('id', firebaseUser.uid);
    } catch (e) {
      debugPrint('Error syncing user with Supabase: $e');
    }
  }

  String _getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'The password is too weak. Please use at least 6 characters.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled. Please contact support.';
      case 'too-many-requests':
        return 'Too many attempts. Please try again later.';
      case 'network-request-failed':
        return 'Network error. Please check your connection.';
      case 'invalid-verification-code':
        return 'Invalid verification code. Please try again.';
      case 'invalid-verification-id':
        return 'Invalid verification ID. Please request a new code.';
      case 'session-expired':
        return 'Session expired. Please sign in again.';
      default:
        return 'An error occurred. Please try again.';
    }
  }
}

// Enums and helper classes
enum AuthProvider { email, google, apple, phone, anonymous }

class AuthResult {
  final bool isSuccess;
  final String? userId;
  final String? email;
  final String? message;
  final String? errorCode;
  final AuthProvider? provider;
  final bool? isNewUser;
  final Map<String, dynamic>? userData;

  const AuthResult.success({required this.userId, required this.email, required this.provider, this.isNewUser, this.userData})
    : isSuccess = true,
      message = null,
      errorCode = null;

  const AuthResult.error({required this.message, this.errorCode, this.provider})
    : isSuccess = false,
      userId = null,
      email = null,
      isNewUser = null,
      userData = null;

  const AuthResult.pending({required this.message, this.provider})
    : isSuccess = false,
      userId = null,
      email = null,
      errorCode = null,
      isNewUser = null,
      userData = null;
}
