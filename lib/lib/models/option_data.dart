// class OptionData {
//   final double strikePrice;
//   final String optionType; // 'CE' or 'PE'
//   final double lastPrice;
//   final double openInterest;
//   final double volume;
//   final double impliedVolatility;
//   final double delta;
//   final double theta;
//   final double gamma;
//   final double vega;
//   final DateTime timestamp;
//   final double priceChange;
//   final double priceChangePercent;
//   final double openInterestChange;

//   OptionData({
//     required this.strikePrice,
//     required this.optionType,
//     required this.lastPrice,
//     required this.openInterest,
//     required this.volume,
//     required this.impliedVolatility,
//     required this.delta,
//     required this.theta,
//     required this.gamma,
//     required this.vega,
//     required this.timestamp,
//     required this.priceChange,
//     required this.priceChangePercent,
//     required this.openInterestChange,
//   });

//   factory OptionData.fromJson(Map<String, dynamic> json) {
//     return OptionData(
//       strikePrice: (json['strike_price'] as num).toDouble(),
//       optionType: json['option_type'] as String,
//       lastPrice: (json['last_price'] as num).toDouble(),
//       openInterest: (json['open_interest'] as num).toDouble(),
//       volume: (json['volume'] as num).toDouble(),
//       impliedVolatility: (json['implied_volatility'] as num).toDouble(),
//       delta: (json['delta'] as num).toDouble(),
//       theta: (json['theta'] as num).toDouble(),
//       gamma: (json['gamma'] as num).toDouble(),
//       vega: (json['vega'] as num).toDouble(),
//       timestamp: DateTime.parse(json['timestamp'] as String),
//       priceChange: (json['price_change'] as num).toDouble(),
//       priceChangePercent: (json['price_change_percent'] as num).toDouble(),
//       openInterestChange: (json['open_interest_change'] as num).toDouble(),
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'strike_price': strikePrice,
//       'option_type': optionType,
//       'last_price': lastPrice,
//       'open_interest': openInterest,
//       'volume': volume,
//       'implied_volatility': impliedVolatility,
//       'delta': delta,
//       'theta': theta,
//       'gamma': gamma,
//       'vega': vega,
//       'timestamp': timestamp.toIso8601String(),
//       'price_change': priceChange,
//       'price_change_percent': priceChangePercent,
//       'open_interest_change': openInterestChange,
//     };
//   }
// }

// class OptionsChainSummary {
//   final double spotPrice;
//   final double atmStrike;
//   final double totalCallOI;
//   final double totalPutOI;
//   final double totalCallVolume;
//   final double totalPutVolume;
//   final double averageCallIV;
//   final double averagePutIV;
//   final double putCallRatio;
//   final DateTime timestamp;

//   OptionsChainSummary({
//     required this.spotPrice,
//     required this.atmStrike,
//     required this.totalCallOI,
//     required this.totalPutOI,
//     required this.totalCallVolume,
//     required this.totalPutVolume,
//     required this.averageCallIV,
//     required this.averagePutIV,
//     required this.putCallRatio,
//     required this.timestamp,
//   });

//   factory OptionsChainSummary.fromJson(Map<String, dynamic> json) {
//     return OptionsChainSummary(
//       spotPrice: (json['spot_price'] as num).toDouble(),
//       atmStrike: (json['atm_strike'] as num).toDouble(),
//       totalCallOI: (json['total_call_oi'] as num).toDouble(),
//       totalPutOI: (json['total_put_oi'] as num).toDouble(),
//       totalCallVolume: (json['total_call_volume'] as num).toDouble(),
//       totalPutVolume: (json['total_put_volume'] as num).toDouble(),
//       averageCallIV: (json['average_call_iv'] as num).toDouble(),
//       averagePutIV: (json['average_put_iv'] as num).toDouble(),
//       putCallRatio: (json['put_call_ratio'] as num).toDouble(),
//       timestamp: DateTime.parse(json['timestamp'] as String),
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'spot_price': spotPrice,
//       'atm_strike': atmStrike,
//       'total_call_oi': totalCallOI,
//       'total_put_oi': totalPutOI,
//       'total_call_volume': totalCallVolume,
//       'total_put_volume': totalPutVolume,
//       'average_call_iv': averageCallIV,
//       'average_put_iv': averagePutIV,
//       'put_call_ratio': putCallRatio,
//       'timestamp': timestamp.toIso8601String(),
//     };
//   }
// }
