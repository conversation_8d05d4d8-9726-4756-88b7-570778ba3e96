// import 'package:supabase_flutter/supabase_flutter.dart';
// import 'package:supabase/supabase.dart';

// class OptionsService {
//   final SupabaseClient client;

//   OptionsService(this.client);

//   /// Fetches the latest options chain data for a given expiry date.
//   /// Groups data by strike price and option type, taking only the latest record.
//   Future<List<OptionData>> getLatestOptionsChain(DateTime expiryDate) async {
//     try {
//       // First, get the latest timestamp for the given expiry date
//       final latestTimestampResponse =
//           await client
//               .from('nifty')
//               .select('timestamp')
//               .eq('expiry_date', expiryDate.toIso8601String())
//               .order('timestamp', ascending: false)
//               .limit(1)
//               .single()
//               .execute();

//       if (latestTimestampResponse.error != null) {
//         throw latestTimestampResponse.error!;
//       }

//       final latestTimestamp = DateTime.parse(latestTimestampResponse.data['timestamp']);

//       // Then fetch all options data for that timestamp
//       final response =
//           await client
//               .from('nifty')
//               .select()
//               .eq('expiry_date', expiryDate.toIso8601String())
//               .eq('timestamp', latestTimestamp.toIso8601String())
//               .order('strike_price', ascending: true)
//               .execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       return (response.data as List).map((json) => OptionData.fromJson(json)).toList();
//     } catch (e) {
//       throw Exception('Failed to fetch latest options chain: $e');
//     }
//   }

//   /// Fetches all available expiry dates
//   Future<List<DateTime>> getAvailableExpiryDates() async {
//     try {
//       final response = await client.from('nifty').select('expiry_date').order('expiry_date', ascending: true).execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       final uniqueDates = <DateTime>{};
//       for (var item in response.data as List) {
//         uniqueDates.add(DateTime.parse(item['expiry_date']));
//       }

//       return uniqueDates.toList()..sort();
//     } catch (e) {
//       throw Exception('Failed to fetch expiry dates: $e');
//     }
//   }

//   /// Fetches the latest detailed options chain data for a given expiry date.
//   /// Uses the optimized option_chain_detailed_view.
//   Future<List<DetailedOptionChainRow>> getDetailedOptionsChain(DateTime expiryDate) async {
//     try {
//       final response =
//           await client
//               .from('option_chain_detailed_view')
//               .select()
//               .eq('expiry_date', expiryDate.toIso8601String())
//               .order('strike_price', ascending: true)
//               .execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       return (response.data as List).map((json) => DetailedOptionChainRow.fromJson(json)).toList();
//     } catch (e) {
//       throw Exception('Failed to fetch detailed options chain: $e');
//     }
//   }

//   /// Fetches intraday changes for options
//   Future<List<IntradayChange>> getIntradayChanges(DateTime expiryDate) async {
//     try {
//       final response =
//           await client
//               .from('intraday_changes_view')
//               .select()
//               .eq('expiry_date', expiryDate.toIso8601String())
//               .order('strike_price', ascending: true)
//               .execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       return (response.data as List).map((json) => IntradayChange.fromJson(json)).toList();
//     } catch (e) {
//       throw Exception('Failed to fetch intraday changes: $e');
//     }
//   }

//   /// Gets a summary of the options chain using the optimized summary view
//   @override
//   Future<OptionsChainSummary> getOptionsChainSummary(DateTime expiryDate) async {
//     try {
//       final response =
//           await client
//               .from('option_chain_summary_view')
//               .select()
//               .eq('expiry_date', expiryDate.toIso8601String())
//               .single()
//               .execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       final data = response.data;
//       return OptionsChainSummary(
//         expiryDate: expiryDate,
//         spotPrice: data['spot_price'],
//         atmStrike: _findATMStrike(await getLatestOptionsChain(expiryDate), data['spot_price']),
//         totalCEOI: data['total_ce_oi'],
//         totalPEOI: data['total_pe_oi'],
//         pcr: data['pcr'],
//         timestamp: DateTime.parse(data['timestamp']),
//         avgIV: data['avg_iv'],
//         avgCEIV: data['avg_ce_iv'],
//         avgPEIV: data['avg_pe_iv'],
//         totalStrikes: data['total_strikes'],
//       );
//     } catch (e) {
//       throw Exception('Failed to fetch options chain summary: $e');
//     }
//   }

//   /// Fetches significant OI changes in the last hour
//   Future<List<OptionChange>> getSignificantOIChanges(DateTime expiryDate, {int minOIChange = 100000}) async {
//     try {
//       final response =
//           await client
//               .from('option_chain_changes_view')
//               .select()
//               .eq('expiry_date', expiryDate.toIso8601String())
//               .gte('minutes_since_last_update', 0)
//               .lte('minutes_since_last_update', 60)
//               .or('oi_change.gte.$minOIChange,oi_change.lte.-$minOIChange')
//               .order('timestamp', ascending: false)
//               .execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       return (response.data as List).map((json) => OptionChange.fromJson(json)).toList();
//     } catch (e) {
//       throw Exception('Failed to fetch significant OI changes: $e');
//     }
//   }

//   /// Helper method to find ATM strike price
//   double _findATMStrike(List<OptionData> data, double spotPrice) {
//     return data.map((e) => e.strikePrice).toSet().reduce((a, b) => (a - spotPrice).abs() < (b - spotPrice).abs() ? a : b);
//   }
// }

// /// Model class for option chain summary
// class OptionsChainSummary {
//   final DateTime expiryDate;
//   final double spotPrice;
//   final double atmStrike;
//   final int totalCEOI;
//   final int totalPEOI;
//   final double pcr;
//   final DateTime timestamp;
//   final double avgIV;
//   final double avgCEIV;
//   final double avgPEIV;
//   final int totalStrikes;

//   OptionsChainSummary({
//     required this.expiryDate,
//     required this.spotPrice,
//     required this.atmStrike,
//     required this.totalCEOI,
//     required this.totalPEOI,
//     required this.pcr,
//     required this.timestamp,
//     required this.avgIV,
//     required this.avgCEIV,
//     required this.avgPEIV,
//     required this.totalStrikes,
//   });
// }

// /// Model class for option data
// class OptionData {
//   final DateTime expiryDate;
//   final DateTime timestamp;
//   final double strikePrice;
//   final double spotPrice;
//   final double lastPrice;
//   final int openInterest;
//   final int volume;
//   final double impliedVolatility;
//   final double delta;
//   final double theta;
//   final String optionType;

//   OptionData({
//     required this.expiryDate,
//     required this.timestamp,
//     required this.strikePrice,
//     required this.spotPrice,
//     required this.lastPrice,
//     required this.openInterest,
//     required this.volume,
//     required this.impliedVolatility,
//     required this.delta,
//     required this.theta,
//     required this.optionType,
//   });

//   factory OptionData.fromJson(Map<String, dynamic> json) {
//     return OptionData(
//       expiryDate: DateTime.parse(json['expiry_date']),
//       timestamp: DateTime.parse(json['timestamp']),
//       strikePrice: json['strike_price'].toDouble(),
//       spotPrice: json['spot_price'].toDouble(),
//       lastPrice: json['last_price'].toDouble(),
//       openInterest: json['oi'],
//       volume: json['volume'],
//       impliedVolatility: json['iv'].toDouble(),
//       delta: json['delta'].toDouble(),
//       theta: json['theta'].toDouble(),
//       optionType: json['option_type'],
//     );
//   }
// }

// /// Model class for detailed option chain row
// class DetailedOptionChainRow {
//   final double strikePrice;
//   final OptionSideData? ce;
//   final OptionSideData? pe;
//   final double totalOI;
//   final double pcr;

//   DetailedOptionChainRow({required this.strikePrice, this.ce, this.pe, required this.totalOI, required this.pcr});

//   factory DetailedOptionChainRow.fromJson(Map<String, dynamic> json) {
//     return DetailedOptionChainRow(
//       strikePrice: json['strike_price'].toDouble(),
//       ce: json['ce'] != null ? OptionSideData.fromJson(json['ce']) : null,
//       pe: json['pe'] != null ? OptionSideData.fromJson(json['pe']) : null,
//       totalOI: json['total_oi'].toDouble(),
//       pcr: json['pcr'].toDouble(),
//     );
//   }
// }

// /// Model class for option side data (CE or PE)
// class OptionSideData {
//   final double lastPrice;
//   final double priceChange;
//   final double priceChangePercent;
//   final int openInterest;
//   final int oiChange;
//   final int volume;
//   final double iv;
//   final double delta;
//   final double theta;

//   OptionSideData({
//     required this.lastPrice,
//     required this.priceChange,
//     required this.priceChangePercent,
//     required this.openInterest,
//     required this.oiChange,
//     required this.volume,
//     required this.iv,
//     required this.delta,
//     required this.theta,
//   });

//   factory OptionSideData.fromJson(Map<String, dynamic> json) {
//     return OptionSideData(
//       lastPrice: json['last_price'].toDouble(),
//       priceChange: json['price_change'].toDouble(),
//       priceChangePercent: json['price_change_percent'].toDouble(),
//       openInterest: json['oi'],
//       oiChange: json['oi_change'],
//       volume: json['volume'],
//       iv: json['iv'].toDouble(),
//       delta: json['delta'].toDouble(),
//       theta: json['theta'].toDouble(),
//     );
//   }
// }

// /// Model class for intraday changes
// class IntradayChange {
//   final DateTime expiryDate;
//   final double strikePrice;
//   final String optionType;
//   final double dayOpen;
//   final double currentPrice;
//   final double dayHigh;
//   final double dayLow;
//   final double priceChange;
//   final double priceChangePercent;
//   final int oiChange;
//   final DateTime timestamp;

//   IntradayChange({
//     required this.expiryDate,
//     required this.strikePrice,
//     required this.optionType,
//     required this.dayOpen,
//     required this.currentPrice,
//     required this.dayHigh,
//     required this.dayLow,
//     required this.priceChange,
//     required this.priceChangePercent,
//     required this.oiChange,
//     required this.timestamp,
//   });

//   factory IntradayChange.fromJson(Map<String, dynamic> json) {
//     return IntradayChange(
//       expiryDate: DateTime.parse(json['expiry_date']),
//       strikePrice: json['strike_price'].toDouble(),
//       optionType: json['option_type'],
//       dayOpen: json['day_open'].toDouble(),
//       currentPrice: json['current_price'].toDouble(),
//       dayHigh: json['day_high'].toDouble(),
//       dayLow: json['day_low'].toDouble(),
//       priceChange: json['price_change'].toDouble(),
//       priceChangePercent: json['price_change_percent'].toDouble(),
//       oiChange: json['oi_change'],
//       timestamp: DateTime.parse(json['timestamp']),
//     );
//   }
// }

// /// Model class for option chain changes
// class OptionChange {
//   final DateTime expiryDate;
//   final DateTime timestamp;
//   final double strikePrice;
//   final String optionType;
//   final double lastPrice;
//   final int oi;
//   final double priceChange;
//   final double priceChangePercent;
//   final int oiChange;
//   final double minutesSinceLastUpdate;

//   OptionChange({
//     required this.expiryDate,
//     required this.timestamp,
//     required this.strikePrice,
//     required this.optionType,
//     required this.lastPrice,
//     required this.oi,
//     required this.priceChange,
//     required this.priceChangePercent,
//     required this.oiChange,
//     required this.minutesSinceLastUpdate,
//   });

//   factory OptionChange.fromJson(Map<String, dynamic> json) {
//     return OptionChange(
//       expiryDate: DateTime.parse(json['expiry_date']),
//       timestamp: DateTime.parse(json['timestamp']),
//       strikePrice: json['strike_price'].toDouble(),
//       optionType: json['option_type'],
//       lastPrice: json['last_price'].toDouble(),
//       oi: json['oi'],
//       priceChange: json['price_change'].toDouble(),
//       priceChangePercent: json['price_change_percent'].toDouble(),
//       oiChange: json['oi_change'],
//       minutesSinceLastUpdate: json['minutes_since_last_update'].toDouble(),
//     );
//   }
// }

// // Add new methods to OptionsService class
// extension OptionsServiceExtension on OptionsService {
//   /// Gets significant OI changes (above threshold)
//   Future<List<DetailedOptionChainRow>> getSignificantOIChanges(
//     DateTime expiryDate, {
//     double threshold = 10.0, // 10% change threshold
//   }) async {
//     try {
//       final response =
//           await client
//               .from('option_chain_changes_view')
//               .select()
//               .eq('expiry_date', expiryDate.toIso8601String())
//               .or('oi_change_percent.gt.$threshold,oi_change_percent.lt.-$threshold')
//               .order('oi_change_percent', ascending: false)
//               .execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       return (response.data as List).map((json) => DetailedOptionChainRow.fromJson(json)).toList();
//     } catch (e) {
//       throw Exception('Failed to fetch significant OI changes: $e');
//     }
//   }

//   /// Gets option chain summary statistics
//   Future<Map<String, dynamic>> getChainSummaryStats(DateTime expiryDate) async {
//     try {
//       final response =
//           await client
//               .from('option_chain_summary_view')
//               .select()
//               .eq('expiry_date', expiryDate.toIso8601String())
//               .single()
//               .execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       return response.data;
//     } catch (e) {
//       throw Exception('Failed to fetch chain summary stats: $e');
//     }
//   }
// }
