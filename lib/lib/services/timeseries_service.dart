// import 'package:supabase_flutter/supabase_flutter.dart';

// /// Model for OHLCV data
// class OHLCVData {
//   final DateTime timestamp;
//   final double open;
//   final double high;
//   final double low;
//   final double close;
//   final int volume;
//   final int openInterest;

//   OHLCVData({
//     required this.timestamp,
//     required this.open,
//     required this.high,
//     required this.low,
//     required this.close,
//     required this.volume,
//     required this.openInterest,
//   });

//   factory OHLCVData.fromJson(Map<String, dynamic> json) {
//     return OHLCVData(
//       timestamp: DateTime.parse(json['bucket']),
//       open: (json['open'] as num).toDouble(),
//       high: (json['high'] as num).toDouble(),
//       low: (json['low'] as num).toDouble(),
//       close: (json['close'] as num).toDouble(),
//       volume: json['volume'] as int,
//       openInterest: json['oi'] as int,
//     );
//   }
// }

// /// Model for IV term structure
// class IVTermStructure {
//   final DateTime expiryDate;
//   final double? avgCallIV;
//   final double? avgPutIV;

//   IVTermStructure({required this.expiryDate, this.avgCallIV, this.avgPutIV});

//   factory IVTermStructure.fromJson(Map<String, dynamic> json) {
//     return IVTermStructure(
//       expiryDate: DateTime.parse(json['expiry_date']),
//       avgCallIV: json['avg_call_iv'] != null ? (json['avg_call_iv'] as num).toDouble() : null,
//       avgPutIV: json['avg_put_iv'] != null ? (json['avg_put_iv'] as num).toDouble() : null,
//     );
//   }
// }

// class TimeseriesService {
//   final SupabaseClient _client;

//   TimeseriesService(this._client);

//   /// Get OHLCV data for a specific option
//   Future<List<OHLCVData>> getOHLCVData({
//     required DateTime expiryDate,
//     required double strikePrice,
//     required String optionType,
//     String interval = '5 minutes',
//     DateTime? startTime,
//     DateTime? endTime,
//   }) async {
//     try {
//       final query = _client
//           .from('nifty_5min_ohlcv')
//           .select()
//           .eq('expiry_date', expiryDate.toIso8601String())
//           .eq('strike_price', strikePrice)
//           .eq('option_type', optionType)
//           .order('bucket', ascending: true);

//       if (startTime != null) {
//         query.gte('bucket', startTime.toIso8601String());
//       }
//       if (endTime != null) {
//         query.lte('bucket', endTime.toIso8601String());
//       }

//       final response = await query.execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       return (response.data as List).map((json) => OHLCVData.fromJson(json)).toList();
//     } catch (e) {
//       throw Exception('Failed to fetch OHLCV data: $e');
//     }
//   }

//   /// Get daily statistics for options
//   Future<Map<String, dynamic>> getDailyStats({
//     required DateTime expiryDate,
//     required double strikePrice,
//     required String optionType,
//     DateTime? date,
//   }) async {
//     try {
//       final query = _client
//           .from('nifty_daily_stats')
//           .select()
//           .eq('expiry_date', expiryDate.toIso8601String())
//           .eq('strike_price', strikePrice)
//           .eq('option_type', optionType);

//       if (date != null) {
//         query.eq('day', DateTime(date.year, date.month, date.day).toIso8601String());
//       }

//       final response = await query.single().execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       return response.data;
//     } catch (e) {
//       throw Exception('Failed to fetch daily stats: $e');
//     }
//   }

//   /// Get IV term structure
//   Future<List<IVTermStructure>> getIVTermStructure({DateTime? timestamp}) async {
//     try {
//       final response =
//           await _client
//               .rpc(
//                 'get_iv_term_structure',
//                 params: {'p_timestamp': timestamp?.toIso8601String() ?? DateTime.now().toIso8601String()},
//               )
//               .execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       return (response.data as List).map((json) => IVTermStructure.fromJson(json)).toList();
//     } catch (e) {
//       throw Exception('Failed to fetch IV term structure: $e');
//     }
//   }

//   /// Get price movement with custom interval
//   Future<List<OHLCVData>> getPriceMovement({
//     required DateTime expiryDate,
//     required double strikePrice,
//     required String optionType,
//     String interval = '5 minutes',
//   }) async {
//     try {
//       final response =
//           await _client
//               .rpc(
//                 'get_price_movement',
//                 params: {
//                   'p_expiry_date': expiryDate.toIso8601String(),
//                   'p_strike_price': strikePrice,
//                   'p_option_type': optionType,
//                   'p_interval': interval,
//                 },
//               )
//               .execute();

//       if (response.error != null) {
//         throw response.error!;
//       }

//       return (response.data as List).map((json) => OHLCVData.fromJson(json)).toList();
//     } catch (e) {
//       throw Exception('Failed to fetch price movement: $e');
//     }
//   }
// }
