// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import 'package:fl_chart/fl_chart.dart';
// import '../providers/timeseries_provider.dart';
// import '../services/timeseries_service.dart';

// class OptionChartWidget extends StatefulWidget {
//   final DateTime expiryDate;
//   final double strikePrice;
//   final String optionType;

//   const OptionChartWidget({Key? key, required this.expiryDate, required this.strikePrice, required this.optionType})
//     : super(key: key);

//   @override
//   State<OptionChartWidget> createState() => _OptionChartWidgetState();
// }

// class _OptionChartWidgetState extends State<OptionChartWidget> {
//   String _selectedInterval = '5 minutes';
//   final List<String> _intervals = ['1 minute', '5 minutes', '15 minutes', '1 hour'];

//   @override
//   void initState() {
//     super.initState();
//     _loadData();
//   }

//   Future<void> _loadData() async {
//     final provider = context.read<TimeseriesProvider>();
//     await provider.loadOHLCVData(
//       expiryDate: widget.expiryDate,
//       strikePrice: widget.strikePrice,
//       optionType: widget.optionType,
//       interval: _selectedInterval,
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Consumer<TimeseriesProvider>(
//       builder: (context, provider, child) {
//         if (provider.isLoading) {
//           return const Center(child: CircularProgressIndicator());
//         }

//         if (provider.error != null) {
//           return Center(child: Text('Error: ${provider.error}'));
//         }

//         final data = provider.ohlcvData;
//         if (data.isEmpty) {
//           return const Center(child: Text('No data available'));
//         }

//         return Column(
//           children: [
//             _buildIntervalSelector(),
//             const SizedBox(height: 16),
//             _buildPriceChart(data),
//             const SizedBox(height: 16),
//             _buildVolumeChart(data),
//           ],
//         );
//       },
//     );
//   }

//   Widget _buildIntervalSelector() {
//     return DropdownButton<String>(
//       value: _selectedInterval,
//       items:
//           _intervals.map((String interval) {
//             return DropdownMenuItem<String>(value: interval, child: Text(interval));
//           }).toList(),
//       onChanged: (String? newValue) {
//         if (newValue != null) {
//           setState(() {
//             _selectedInterval = newValue;
//           });
//           _loadData();
//         }
//       },
//     );
//   }

//   Widget _buildPriceChart(List<OHLCVData> data) {
//     return SizedBox(
//       height: 300,
//       child: LineChart(
//         LineChartData(
//           gridData: const FlGridData(show: true),
//           titlesData: const FlTitlesData(show: true),
//           borderData: FlBorderData(show: true),
//           lineBarsData: [
//             LineChartBarData(
//               spots:
//                   data.map((point) {
//                     return FlSpot(point.timestamp.millisecondsSinceEpoch.toDouble(), point.close);
//                   }).toList(),
//               isCurved: true,
//               color: Colors.blue,
//               barWidth: 2,
//               dotData: const FlDotData(show: false),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildVolumeChart(List<OHLCVData> data) {
//     return SizedBox(
//       height: 150,
//       child: BarChart(
//         BarChartData(
//           gridData: const FlGridData(show: true),
//           titlesData: const FlTitlesData(show: true),
//           borderData: FlBorderData(show: true),
//           barGroups:
//               data.asMap().entries.map((entry) {
//                 return BarChartGroupData(
//                   x: entry.key,
//                   barRods: [
//                     BarChartRodData(y: entry.value.volume.toDouble(), colors: [Colors.green]),
//                   ],
//                 );
//               }).toList(),
//         ),
//       ),
//     );
//   }
// }
