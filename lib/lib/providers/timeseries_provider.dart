// import 'package:flutter/foundation.dart';
// import '../services/timeseries_service.dart';

// class TimeseriesProvider with ChangeNotifier {
//   final TimeseriesService _service;
//   List<OHLCVData> _ohlcvData = [];
//   List<IVTermStructure> _ivTermStructure = [];
//   bool _isLoading = false;
//   String? _error;

//   TimeseriesProvider(this._service);

//   List<OHLCVData> get ohlcvData => _ohlcvData;
//   List<IVTermStructure> get ivTermStructure => _ivTermStructure;
//   bool get isLoading => _isLoading;
//   String? get error => _error;

//   /// Load OHLCV data for a specific option
//   Future<void> loadOHLCVData({
//     required DateTime expiryDate,
//     required double strikePrice,
//     required String optionType,
//     String interval = '5 minutes',
//     DateTime? startTime,
//     DateTime? endTime,
//   }) async {
//     try {
//       _isLoading = true;
//       _error = null;
//       notifyListeners();

//       _ohlcvData = await _service.getOHLCVData(
//         expiryDate: expiryDate,
//         strikePrice: strikePrice,
//         optionType: optionType,
//         interval: interval,
//         startTime: startTime,
//         endTime: endTime,
//       );

//       notifyListeners();
//     } catch (e) {
//       _error = e.toString();
//       notifyListeners();
//     } finally {
//       _isLoading = false;
//       notifyListeners();
//     }
//   }

//   /// Load IV term structure
//   Future<void> loadIVTermStructure({DateTime? timestamp}) async {
//     try {
//       _isLoading = true;
//       _error = null;
//       notifyListeners();

//       _ivTermStructure = await _service.getIVTermStructure(timestamp: timestamp);

//       notifyListeners();
//     } catch (e) {
//       _error = e.toString();
//       notifyListeners();
//     } finally {
//       _isLoading = false;
//       notifyListeners();
//     }
//   }

//   /// Get daily statistics for an option
//   Future<Map<String, dynamic>> getDailyStats({
//     required DateTime expiryDate,
//     required double strikePrice,
//     required String optionType,
//     DateTime? date,
//   }) async {
//     try {
//       _isLoading = true;
//       _error = null;
//       notifyListeners();

//       final stats = await _service.getDailyStats(
//         expiryDate: expiryDate,
//         strikePrice: strikePrice,
//         optionType: optionType,
//         date: date,
//       );

//       return stats;
//     } catch (e) {
//       _error = e.toString();
//       throw Exception(_error);
//     } finally {
//       _isLoading = false;
//       notifyListeners();
//     }
//   }

//   /// Get custom interval price movement
//   Future<void> loadPriceMovement({
//     required DateTime expiryDate,
//     required double strikePrice,
//     required String optionType,
//     String interval = '5 minutes',
//   }) async {
//     try {
//       _isLoading = true;
//       _error = null;
//       notifyListeners();

//       _ohlcvData = await _service.getPriceMovement(
//         expiryDate: expiryDate,
//         strikePrice: strikePrice,
//         optionType: optionType,
//         interval: interval,
//       );

//       notifyListeners();
//     } catch (e) {
//       _error = e.toString();
//       notifyListeners();
//     } finally {
//       _isLoading = false;
//       notifyListeners();
//     }
//   }
// }
