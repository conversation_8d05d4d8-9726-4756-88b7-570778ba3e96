// import 'package:flutter/foundation.dart';
// import 'package:supabase_flutter/supabase_flutter.dart';
// import '../models/option_data.dart';

// class OptionsProvider with ChangeNotifier {
//   final SupabaseClient _supabase;
//   List<OptionData> _optionsChain = [];
//   List<String> _expiryDates = [];
//   OptionsChainSummary? _summary;
//   String? _selectedExpiry;
//   bool _isLoading = false;
//   String? _error;

//   OptionsProvider(this._supabase);

//   List<OptionData> get optionsChain => _optionsChain;
//   List<String> get expiryDates => _expiryDates;
//   OptionsChainSummary? get summary => _summary;
//   String? get selectedExpiry => _selectedExpiry;
//   bool get isLoading => _isLoading;
//   String? get error => _error;

//   Future<void> initialize() async {
//     await loadExpiryDates();
//     if (_expiryDates.isNotEmpty) {
//       await selectExpiry(_expiryDates.first);
//     }
//   }

//   Future<void> loadExpiryDates() async {
//     try {
//       _isLoading = true;
//       notifyListeners();

//       final response = await _supabase.from('option_chain_summary_view').select('expiry_date').order('expiry_date');

//       _expiryDates = (response as List).map((e) => e['expiry_date'] as String).toList();

//       _error = null;
//     } catch (e) {
//       _error = 'Failed to load expiry dates: $e';
//     } finally {
//       _isLoading = false;
//       notifyListeners();
//     }
//   }

//   Future<void> selectExpiry(String expiry) async {
//     try {
//       _isLoading = true;
//       _selectedExpiry = expiry;
//       notifyListeners();

//       await Future.wait([_loadOptionsChain(), _loadSummary()]);

//       _error = null;
//     } catch (e) {
//       _error = 'Failed to load options data: $e';
//     } finally {
//       _isLoading = false;
//       notifyListeners();
//     }
//   }

//   Future<void> _loadOptionsChain() async {
//     final response = await _supabase
//         .from('option_chain_detailed_view')
//         .select()
//         .eq('expiry_date', _selectedExpiry)
//         .order('strike_price');

//     _optionsChain = (response as List).map((json) => OptionData.fromJson(json)).toList();
//   }

//   Future<void> _loadSummary() async {
//     final response = await _supabase.from('option_chain_summary_view').select().eq('expiry_date', _selectedExpiry).single();

//     _summary = OptionsChainSummary.fromJson(response);
//   }

//   List<OptionData> getCallOptions() {
//     return _optionsChain.where((option) => option.optionType == 'CE').toList();
//   }

//   List<OptionData> getPutOptions() {
//     return _optionsChain.where((option) => option.optionType == 'PE').toList();
//   }

//   OptionData? getATMCall() {
//     if (_summary == null) return null;
//     return _optionsChain.firstWhere(
//       (option) => option.optionType == 'CE' && option.strikePrice == _summary!.atmStrike,
//       orElse: () => throw Exception('ATM Call option not found'),
//     );
//   }

//   OptionData? getATMPut() {
//     if (_summary == null) return null;
//     return _optionsChain.firstWhere(
//       (option) => option.optionType == 'PE' && option.strikePrice == _summary!.atmStrike,
//       orElse: () => throw Exception('ATM Put option not found'),
//     );
//   }

//   List<OptionData> getSignificantOIChanges({required double threshold, String? optionType}) {
//     return _optionsChain
//         .where(
//           (option) => (optionType == null || option.optionType == optionType) && option.openInterestChange.abs() >= threshold,
//         )
//         .toList();
//   }

//   void refresh() async {
//     if (_selectedExpiry != null) {
//       await selectExpiry(_selectedExpiry!);
//     }
//   }
// }
