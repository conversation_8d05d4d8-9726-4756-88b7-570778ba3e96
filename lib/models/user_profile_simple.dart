// Temporary simple models without Freezed to get the foundation working
// We'll migrate back to Freezed once the formatting issues are resolved

class UserProfile {
  final String id;
  final String email;
  final String? fullName;
  final String? avatarUrl;
  final String? phone;
  final bool isOnboarded;
  final String subscriptionTier;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic> preferences;

  const UserProfile({
    required this.id,
    required this.email,
    this.fullName,
    this.avatarUrl,
    this.phone,
    this.isOnboarded = false,
    this.subscriptionTier = 'free',
    this.createdAt,
    this.updatedAt,
    this.preferences = const {},
  });

  UserProfile copyWith({
    String? id,
    String? email,
    String? fullName,
    String? avatarUrl,
    String? phone,
    bool? isOnboarded,
    String? subscriptionTier,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? preferences,
  }) {
    return UserProfile(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      phone: phone ?? this.phone,
      isOnboarded: isOnboarded ?? this.isOnboarded,
      subscriptionTier: subscriptionTier ?? this.subscriptionTier,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      preferences: preferences ?? this.preferences,
    );
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      phone: json['phone'] as String?,
      isOnboarded: json['is_onboarded'] as bool? ?? false,
      subscriptionTier: json['subscription_tier'] as String? ?? 'free',
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      preferences: json['preferences'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'avatar_url': avatarUrl,
      'phone': phone,
      'is_onboarded': isOnboarded,
      'subscription_tier': subscriptionTier,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'preferences': preferences,
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserProfile &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          email == other.email &&
          fullName == other.fullName &&
          avatarUrl == other.avatarUrl &&
          phone == other.phone &&
          isOnboarded == other.isOnboarded &&
          subscriptionTier == other.subscriptionTier &&
          createdAt == other.createdAt &&
          updatedAt == other.updatedAt;

  @override
  int get hashCode => Object.hash(id, email, fullName, avatarUrl, phone, isOnboarded, subscriptionTier, createdAt, updatedAt);

  @override
  String toString() {
    return 'UserProfile(id: $id, email: $email, fullName: $fullName, avatarUrl: $avatarUrl, phone: $phone, isOnboarded: $isOnboarded, subscriptionTier: $subscriptionTier, createdAt: $createdAt, updatedAt: $updatedAt, preferences: $preferences)';
  }
}

class OnboardingStep {
  final String id;
  final String title;
  final String description;
  final String icon;
  final bool isCompleted;

  const OnboardingStep({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    this.isCompleted = false,
  });

  OnboardingStep copyWith({String? id, String? title, String? description, String? icon, bool? isCompleted}) {
    return OnboardingStep(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  factory OnboardingStep.fromJson(Map<String, dynamic> json) {
    return OnboardingStep(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      icon: json['icon'] as String,
      isCompleted: json['is_completed'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'title': title, 'description': description, 'icon': icon, 'is_completed': isCompleted};
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OnboardingStep &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          title == other.title &&
          description == other.description &&
          icon == other.icon &&
          isCompleted == other.isCompleted;

  @override
  int get hashCode => Object.hash(id, title, description, icon, isCompleted);

  @override
  String toString() {
    return 'OnboardingStep(id: $id, title: $title, description: $description, icon: $icon, isCompleted: $isCompleted)';
  }
}

class UserPreferences {
  final bool pushNotifications;
  final bool emailNotifications;
  final String themeMode;
  final String currency;
  final String market;
  final List<String> watchlist;
  final Map<String, dynamic> aiAssistantSettings;

  const UserPreferences({
    this.pushNotifications = true,
    this.emailNotifications = true,
    this.themeMode = 'light',
    this.currency = 'USD',
    this.market = 'US',
    this.watchlist = const [],
    this.aiAssistantSettings = const {},
  });

  UserPreferences copyWith({
    bool? pushNotifications,
    bool? emailNotifications,
    String? themeMode,
    String? currency,
    String? market,
    List<String>? watchlist,
    Map<String, dynamic>? aiAssistantSettings,
  }) {
    return UserPreferences(
      pushNotifications: pushNotifications ?? this.pushNotifications,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      themeMode: themeMode ?? this.themeMode,
      currency: currency ?? this.currency,
      market: market ?? this.market,
      watchlist: watchlist ?? this.watchlist,
      aiAssistantSettings: aiAssistantSettings ?? this.aiAssistantSettings,
    );
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      pushNotifications: json['push_notifications'] as bool? ?? true,
      emailNotifications: json['email_notifications'] as bool? ?? true,
      themeMode: json['theme_mode'] as String? ?? 'light',
      currency: json['currency'] as String? ?? 'USD',
      market: json['market'] as String? ?? 'US',
      watchlist: (json['watchlist'] as List<dynamic>?)?.cast<String>() ?? [],
      aiAssistantSettings: json['ai_assistant_settings'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'push_notifications': pushNotifications,
      'email_notifications': emailNotifications,
      'theme_mode': themeMode,
      'currency': currency,
      'market': market,
      'watchlist': watchlist,
      'ai_assistant_settings': aiAssistantSettings,
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserPreferences &&
          runtimeType == other.runtimeType &&
          pushNotifications == other.pushNotifications &&
          emailNotifications == other.emailNotifications &&
          themeMode == other.themeMode &&
          currency == other.currency &&
          market == other.market;

  @override
  int get hashCode => Object.hash(pushNotifications, emailNotifications, themeMode, currency, market);

  @override
  String toString() {
    return 'UserPreferences(pushNotifications: $pushNotifications, emailNotifications: $emailNotifications, themeMode: $themeMode, currency: $currency, market: $market, watchlist: $watchlist, aiAssistantSettings: $aiAssistantSettings)';
  }
}
