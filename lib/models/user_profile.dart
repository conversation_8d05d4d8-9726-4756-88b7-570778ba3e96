import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_profile.freezed.dart';
part 'user_profile.g.dart';

@freezed
abstract class UserProfile with _$UserProfile {
  const factory UserProfile({
    required String id,
    required String email,
    String? fullName,
    String? avatarUrl,
    String? phone,
    @Default(false) bool isOnboarded,
    @Default('free') String subscriptionTier,
    DateTime? createdAt,
    DateTime? updatedAt,
    @Default({}) Map<String, dynamic> preferences,
  }) = _UserProfile;

  factory UserProfile.fromJson(Map<String, dynamic> json) => _$UserProfileFromJson(json);
}

@freezed
abstract class OnboardingStep with _$OnboardingStep {
  const factory OnboardingStep({
    required String id,
    required String title,
    required String description,
    required String icon,
    @Default(false) bool isCompleted,
  }) = _OnboardingStep;

  factory OnboardingStep.fromJson(Map<String, dynamic> json) => _$OnboardingStepFromJson(json);
}

@freezed
abstract class UserPreferences with _$UserPreferences {
  const factory UserPreferences({
    @Default(true) bool pushNotifications,
    @Default(true) bool emailNotifications,
    @Default('light') String themeMode,
    @Default('USD') String currency,
    @Default('US') String market,
    @Default([]) List<String> watchlist,
    @Default({}) Map<String, dynamic> aiAssistantSettings,
  }) = _UserPreferences;

  factory UserPreferences.fromJson(Map<String, dynamic> json) => _$UserPreferencesFromJson(json);
}
