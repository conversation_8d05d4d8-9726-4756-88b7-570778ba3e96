import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_models.freezed.dart';
part 'auth_models.g.dart';

@freezed
class AuthResult with _$AuthResult {
  const factory AuthResult.success({
    required String userId,
    required String email,
    String? accessToken,
    String? refreshToken,
    required AuthProvider provider,
    bool? isNewUser,
    Map<String, dynamic>? userData,
  }) = _AuthSuccess;

  const factory AuthResult.error({required String message, String? errorCode, AuthProvider? provider}) = _AuthError;

  const factory AuthResult.pending({required String message, AuthProvider? provider}) = _AuthPending;
}

@freezed
class PhoneAuthState with _$PhoneAuthState {
  const factory PhoneAuthState.initial() = _PhoneAuthInitial;

  const factory PhoneAuthState.codeSent({required String verificationId, required String phoneNumber, int? resendToken}) =
      _PhoneAuthCodeSent;

  const factory PhoneAuthState.codeVerified({required String phoneNumber}) = _PhoneAuthCodeVerified;

  const factory PhoneAuthState.error({required String message, String? errorCode}) = _PhoneAuthError;

  const factory PhoneAuthState.loading({String? message}) = _PhoneAuthLoading;
}

enum AuthProvider { email, google, apple, phone, anonymous }

extension AuthProviderExtension on AuthProvider {
  String get displayName {
    switch (this) {
      case AuthProvider.email:
        return 'Email';
      case AuthProvider.google:
        return 'Google';
      case AuthProvider.apple:
        return 'Apple';
      case AuthProvider.phone:
        return 'Phone';
      case AuthProvider.anonymous:
        return 'Anonymous';
    }
  }

  String get iconAsset {
    switch (this) {
      case AuthProvider.email:
        return 'assets/icons/email.png';
      case AuthProvider.google:
        return 'assets/icons/google.png';
      case AuthProvider.apple:
        return 'assets/icons/apple.png';
      case AuthProvider.phone:
        return 'assets/icons/phone.png';
      case AuthProvider.anonymous:
        return 'assets/icons/anonymous.png';
    }
  }
}

@freezed
class BiometricAuthState with _$BiometricAuthState {
  const factory BiometricAuthState.initial() = _BiometricAuthInitial;

  const factory BiometricAuthState.available({
    required List<BiometricType> availableBiometrics,
    required bool isDeviceSupported,
  }) = _BiometricAuthAvailable;

  const factory BiometricAuthState.authenticated() = _BiometricAuthAuthenticated;

  const factory BiometricAuthState.error({required String message}) = _BiometricAuthError;

  const factory BiometricAuthState.unavailable() = _BiometricAuthUnavailable;
}

enum BiometricType { face, fingerprint, iris, weak, strong }

@freezed
class AuthConfiguration with _$AuthConfiguration {
  const factory AuthConfiguration({
    @Default(true) bool enableEmailAuth,
    @Default(true) bool enableGoogleAuth,
    @Default(true) bool enableAppleAuth,
    @Default(true) bool enablePhoneAuth,
    @Default(false) bool enableAnonymousAuth,
    @Default(true) bool enableBiometricAuth,
    @Default(false) bool requireEmailVerification,
    @Default(true) bool enablePasswordReset,
    @Default(30) int phoneCodeExpirationMinutes,
    @Default(3) int maxPasswordResetAttempts,
  }) = _AuthConfiguration;

  factory AuthConfiguration.fromJson(Map<String, dynamic> json) => _$AuthConfigurationFromJson(json);
}
