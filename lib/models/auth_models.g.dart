// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AuthConfiguration _$AuthConfigurationFromJson(Map<String, dynamic> json) =>
    _AuthConfiguration(
      enableEmailAuth: json['enableEmailAuth'] as bool? ?? true,
      enableGoogleAuth: json['enableGoogleAuth'] as bool? ?? true,
      enableAppleAuth: json['enableAppleAuth'] as bool? ?? true,
      enablePhoneAuth: json['enablePhoneAuth'] as bool? ?? true,
      enableAnonymousAuth: json['enableAnonymousAuth'] as bool? ?? false,
      enableBiometricAuth: json['enableBiometricAuth'] as bool? ?? true,
      requireEmailVerification:
          json['requireEmailVerification'] as bool? ?? false,
      enablePasswordReset: json['enablePasswordReset'] as bool? ?? true,
      phoneCodeExpirationMinutes:
          (json['phoneCodeExpirationMinutes'] as num?)?.toInt() ?? 30,
      maxPasswordResetAttempts:
          (json['maxPasswordResetAttempts'] as num?)?.toInt() ?? 3,
    );

Map<String, dynamic> _$AuthConfigurationToJson(_AuthConfiguration instance) =>
    <String, dynamic>{
      'enableEmailAuth': instance.enableEmailAuth,
      'enableGoogleAuth': instance.enableGoogleAuth,
      'enableAppleAuth': instance.enableAppleAuth,
      'enablePhoneAuth': instance.enablePhoneAuth,
      'enableAnonymousAuth': instance.enableAnonymousAuth,
      'enableBiometricAuth': instance.enableBiometricAuth,
      'requireEmailVerification': instance.requireEmailVerification,
      'enablePasswordReset': instance.enablePasswordReset,
      'phoneCodeExpirationMinutes': instance.phoneCodeExpirationMinutes,
      'maxPasswordResetAttempts': instance.maxPasswordResetAttempts,
    };
