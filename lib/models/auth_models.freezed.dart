// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AuthResult {

 AuthProvider? get provider;
/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthResultCopyWith<AuthResult> get copyWith => _$AuthResultCopyWithImpl<AuthResult>(this as AuthResult, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthResult&&(identical(other.provider, provider) || other.provider == provider));
}


@override
int get hashCode => Object.hash(runtimeType,provider);

@override
String toString() {
  return 'AuthResult(provider: $provider)';
}


}

/// @nodoc
abstract mixin class $AuthResultCopyWith<$Res>  {
  factory $AuthResultCopyWith(AuthResult value, $Res Function(AuthResult) _then) = _$AuthResultCopyWithImpl;
@useResult
$Res call({
 AuthProvider provider
});




}
/// @nodoc
class _$AuthResultCopyWithImpl<$Res>
    implements $AuthResultCopyWith<$Res> {
  _$AuthResultCopyWithImpl(this._self, this._then);

  final AuthResult _self;
  final $Res Function(AuthResult) _then;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? provider = null,}) {
  return _then(_self.copyWith(
provider: null == provider ? _self.provider! : provider // ignore: cast_nullable_to_non_nullable
as AuthProvider,
  ));
}

}


/// Adds pattern-matching-related methods to [AuthResult].
extension AuthResultPatterns on AuthResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _AuthSuccess value)?  success,TResult Function( _AuthError value)?  error,TResult Function( _AuthPending value)?  pending,required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AuthSuccess() when success != null:
return success(_that);case _AuthError() when error != null:
return error(_that);case _AuthPending() when pending != null:
return pending(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _AuthSuccess value)  success,required TResult Function( _AuthError value)  error,required TResult Function( _AuthPending value)  pending,}){
final _that = this;
switch (_that) {
case _AuthSuccess():
return success(_that);case _AuthError():
return error(_that);case _AuthPending():
return pending(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _AuthSuccess value)?  success,TResult? Function( _AuthError value)?  error,TResult? Function( _AuthPending value)?  pending,}){
final _that = this;
switch (_that) {
case _AuthSuccess() when success != null:
return success(_that);case _AuthError() when error != null:
return error(_that);case _AuthPending() when pending != null:
return pending(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( String userId,  String email,  String? accessToken,  String? refreshToken,  AuthProvider provider,  bool? isNewUser,  Map<String, dynamic>? userData)?  success,TResult Function( String message,  String? errorCode,  AuthProvider? provider)?  error,TResult Function( String message,  AuthProvider? provider)?  pending,required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AuthSuccess() when success != null:
return success(_that.userId,_that.email,_that.accessToken,_that.refreshToken,_that.provider,_that.isNewUser,_that.userData);case _AuthError() when error != null:
return error(_that.message,_that.errorCode,_that.provider);case _AuthPending() when pending != null:
return pending(_that.message,_that.provider);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( String userId,  String email,  String? accessToken,  String? refreshToken,  AuthProvider provider,  bool? isNewUser,  Map<String, dynamic>? userData)  success,required TResult Function( String message,  String? errorCode,  AuthProvider? provider)  error,required TResult Function( String message,  AuthProvider? provider)  pending,}) {final _that = this;
switch (_that) {
case _AuthSuccess():
return success(_that.userId,_that.email,_that.accessToken,_that.refreshToken,_that.provider,_that.isNewUser,_that.userData);case _AuthError():
return error(_that.message,_that.errorCode,_that.provider);case _AuthPending():
return pending(_that.message,_that.provider);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( String userId,  String email,  String? accessToken,  String? refreshToken,  AuthProvider provider,  bool? isNewUser,  Map<String, dynamic>? userData)?  success,TResult? Function( String message,  String? errorCode,  AuthProvider? provider)?  error,TResult? Function( String message,  AuthProvider? provider)?  pending,}) {final _that = this;
switch (_that) {
case _AuthSuccess() when success != null:
return success(_that.userId,_that.email,_that.accessToken,_that.refreshToken,_that.provider,_that.isNewUser,_that.userData);case _AuthError() when error != null:
return error(_that.message,_that.errorCode,_that.provider);case _AuthPending() when pending != null:
return pending(_that.message,_that.provider);case _:
  return null;

}
}

}

/// @nodoc


class _AuthSuccess implements AuthResult {
  const _AuthSuccess({required this.userId, required this.email, this.accessToken, this.refreshToken, required this.provider, this.isNewUser, final  Map<String, dynamic>? userData}): _userData = userData;
  

 final  String userId;
 final  String email;
 final  String? accessToken;
 final  String? refreshToken;
@override final  AuthProvider provider;
 final  bool? isNewUser;
 final  Map<String, dynamic>? _userData;
 Map<String, dynamic>? get userData {
  final value = _userData;
  if (value == null) return null;
  if (_userData is EqualUnmodifiableMapView) return _userData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AuthSuccessCopyWith<_AuthSuccess> get copyWith => __$AuthSuccessCopyWithImpl<_AuthSuccess>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AuthSuccess&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.email, email) || other.email == email)&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.provider, provider) || other.provider == provider)&&(identical(other.isNewUser, isNewUser) || other.isNewUser == isNewUser)&&const DeepCollectionEquality().equals(other._userData, _userData));
}


@override
int get hashCode => Object.hash(runtimeType,userId,email,accessToken,refreshToken,provider,isNewUser,const DeepCollectionEquality().hash(_userData));

@override
String toString() {
  return 'AuthResult.success(userId: $userId, email: $email, accessToken: $accessToken, refreshToken: $refreshToken, provider: $provider, isNewUser: $isNewUser, userData: $userData)';
}


}

/// @nodoc
abstract mixin class _$AuthSuccessCopyWith<$Res> implements $AuthResultCopyWith<$Res> {
  factory _$AuthSuccessCopyWith(_AuthSuccess value, $Res Function(_AuthSuccess) _then) = __$AuthSuccessCopyWithImpl;
@override @useResult
$Res call({
 String userId, String email, String? accessToken, String? refreshToken, AuthProvider provider, bool? isNewUser, Map<String, dynamic>? userData
});




}
/// @nodoc
class __$AuthSuccessCopyWithImpl<$Res>
    implements _$AuthSuccessCopyWith<$Res> {
  __$AuthSuccessCopyWithImpl(this._self, this._then);

  final _AuthSuccess _self;
  final $Res Function(_AuthSuccess) _then;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? email = null,Object? accessToken = freezed,Object? refreshToken = freezed,Object? provider = null,Object? isNewUser = freezed,Object? userData = freezed,}) {
  return _then(_AuthSuccess(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,accessToken: freezed == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String?,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,provider: null == provider ? _self.provider : provider // ignore: cast_nullable_to_non_nullable
as AuthProvider,isNewUser: freezed == isNewUser ? _self.isNewUser : isNewUser // ignore: cast_nullable_to_non_nullable
as bool?,userData: freezed == userData ? _self._userData : userData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class _AuthError implements AuthResult {
  const _AuthError({required this.message, this.errorCode, this.provider});
  

 final  String message;
 final  String? errorCode;
@override final  AuthProvider? provider;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AuthErrorCopyWith<_AuthError> get copyWith => __$AuthErrorCopyWithImpl<_AuthError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AuthError&&(identical(other.message, message) || other.message == message)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode)&&(identical(other.provider, provider) || other.provider == provider));
}


@override
int get hashCode => Object.hash(runtimeType,message,errorCode,provider);

@override
String toString() {
  return 'AuthResult.error(message: $message, errorCode: $errorCode, provider: $provider)';
}


}

/// @nodoc
abstract mixin class _$AuthErrorCopyWith<$Res> implements $AuthResultCopyWith<$Res> {
  factory _$AuthErrorCopyWith(_AuthError value, $Res Function(_AuthError) _then) = __$AuthErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, String? errorCode, AuthProvider? provider
});




}
/// @nodoc
class __$AuthErrorCopyWithImpl<$Res>
    implements _$AuthErrorCopyWith<$Res> {
  __$AuthErrorCopyWithImpl(this._self, this._then);

  final _AuthError _self;
  final $Res Function(_AuthError) _then;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? errorCode = freezed,Object? provider = freezed,}) {
  return _then(_AuthError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,provider: freezed == provider ? _self.provider : provider // ignore: cast_nullable_to_non_nullable
as AuthProvider?,
  ));
}


}

/// @nodoc


class _AuthPending implements AuthResult {
  const _AuthPending({required this.message, this.provider});
  

 final  String message;
@override final  AuthProvider? provider;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AuthPendingCopyWith<_AuthPending> get copyWith => __$AuthPendingCopyWithImpl<_AuthPending>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AuthPending&&(identical(other.message, message) || other.message == message)&&(identical(other.provider, provider) || other.provider == provider));
}


@override
int get hashCode => Object.hash(runtimeType,message,provider);

@override
String toString() {
  return 'AuthResult.pending(message: $message, provider: $provider)';
}


}

/// @nodoc
abstract mixin class _$AuthPendingCopyWith<$Res> implements $AuthResultCopyWith<$Res> {
  factory _$AuthPendingCopyWith(_AuthPending value, $Res Function(_AuthPending) _then) = __$AuthPendingCopyWithImpl;
@override @useResult
$Res call({
 String message, AuthProvider? provider
});




}
/// @nodoc
class __$AuthPendingCopyWithImpl<$Res>
    implements _$AuthPendingCopyWith<$Res> {
  __$AuthPendingCopyWithImpl(this._self, this._then);

  final _AuthPending _self;
  final $Res Function(_AuthPending) _then;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? provider = freezed,}) {
  return _then(_AuthPending(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,provider: freezed == provider ? _self.provider : provider // ignore: cast_nullable_to_non_nullable
as AuthProvider?,
  ));
}


}

/// @nodoc
mixin _$PhoneAuthState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PhoneAuthState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PhoneAuthState()';
}


}

/// @nodoc
class $PhoneAuthStateCopyWith<$Res>  {
$PhoneAuthStateCopyWith(PhoneAuthState _, $Res Function(PhoneAuthState) __);
}


/// Adds pattern-matching-related methods to [PhoneAuthState].
extension PhoneAuthStatePatterns on PhoneAuthState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _PhoneAuthInitial value)?  initial,TResult Function( _PhoneAuthCodeSent value)?  codeSent,TResult Function( _PhoneAuthCodeVerified value)?  codeVerified,TResult Function( _PhoneAuthError value)?  error,TResult Function( _PhoneAuthLoading value)?  loading,required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PhoneAuthInitial() when initial != null:
return initial(_that);case _PhoneAuthCodeSent() when codeSent != null:
return codeSent(_that);case _PhoneAuthCodeVerified() when codeVerified != null:
return codeVerified(_that);case _PhoneAuthError() when error != null:
return error(_that);case _PhoneAuthLoading() when loading != null:
return loading(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _PhoneAuthInitial value)  initial,required TResult Function( _PhoneAuthCodeSent value)  codeSent,required TResult Function( _PhoneAuthCodeVerified value)  codeVerified,required TResult Function( _PhoneAuthError value)  error,required TResult Function( _PhoneAuthLoading value)  loading,}){
final _that = this;
switch (_that) {
case _PhoneAuthInitial():
return initial(_that);case _PhoneAuthCodeSent():
return codeSent(_that);case _PhoneAuthCodeVerified():
return codeVerified(_that);case _PhoneAuthError():
return error(_that);case _PhoneAuthLoading():
return loading(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _PhoneAuthInitial value)?  initial,TResult? Function( _PhoneAuthCodeSent value)?  codeSent,TResult? Function( _PhoneAuthCodeVerified value)?  codeVerified,TResult? Function( _PhoneAuthError value)?  error,TResult? Function( _PhoneAuthLoading value)?  loading,}){
final _that = this;
switch (_that) {
case _PhoneAuthInitial() when initial != null:
return initial(_that);case _PhoneAuthCodeSent() when codeSent != null:
return codeSent(_that);case _PhoneAuthCodeVerified() when codeVerified != null:
return codeVerified(_that);case _PhoneAuthError() when error != null:
return error(_that);case _PhoneAuthLoading() when loading != null:
return loading(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  initial,TResult Function( String verificationId,  String phoneNumber,  int? resendToken)?  codeSent,TResult Function( String phoneNumber)?  codeVerified,TResult Function( String message,  String? errorCode)?  error,TResult Function( String? message)?  loading,required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PhoneAuthInitial() when initial != null:
return initial();case _PhoneAuthCodeSent() when codeSent != null:
return codeSent(_that.verificationId,_that.phoneNumber,_that.resendToken);case _PhoneAuthCodeVerified() when codeVerified != null:
return codeVerified(_that.phoneNumber);case _PhoneAuthError() when error != null:
return error(_that.message,_that.errorCode);case _PhoneAuthLoading() when loading != null:
return loading(_that.message);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  initial,required TResult Function( String verificationId,  String phoneNumber,  int? resendToken)  codeSent,required TResult Function( String phoneNumber)  codeVerified,required TResult Function( String message,  String? errorCode)  error,required TResult Function( String? message)  loading,}) {final _that = this;
switch (_that) {
case _PhoneAuthInitial():
return initial();case _PhoneAuthCodeSent():
return codeSent(_that.verificationId,_that.phoneNumber,_that.resendToken);case _PhoneAuthCodeVerified():
return codeVerified(_that.phoneNumber);case _PhoneAuthError():
return error(_that.message,_that.errorCode);case _PhoneAuthLoading():
return loading(_that.message);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  initial,TResult? Function( String verificationId,  String phoneNumber,  int? resendToken)?  codeSent,TResult? Function( String phoneNumber)?  codeVerified,TResult? Function( String message,  String? errorCode)?  error,TResult? Function( String? message)?  loading,}) {final _that = this;
switch (_that) {
case _PhoneAuthInitial() when initial != null:
return initial();case _PhoneAuthCodeSent() when codeSent != null:
return codeSent(_that.verificationId,_that.phoneNumber,_that.resendToken);case _PhoneAuthCodeVerified() when codeVerified != null:
return codeVerified(_that.phoneNumber);case _PhoneAuthError() when error != null:
return error(_that.message,_that.errorCode);case _PhoneAuthLoading() when loading != null:
return loading(_that.message);case _:
  return null;

}
}

}

/// @nodoc


class _PhoneAuthInitial implements PhoneAuthState {
  const _PhoneAuthInitial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PhoneAuthInitial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PhoneAuthState.initial()';
}


}




/// @nodoc


class _PhoneAuthCodeSent implements PhoneAuthState {
  const _PhoneAuthCodeSent({required this.verificationId, required this.phoneNumber, this.resendToken});
  

 final  String verificationId;
 final  String phoneNumber;
 final  int? resendToken;

/// Create a copy of PhoneAuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PhoneAuthCodeSentCopyWith<_PhoneAuthCodeSent> get copyWith => __$PhoneAuthCodeSentCopyWithImpl<_PhoneAuthCodeSent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PhoneAuthCodeSent&&(identical(other.verificationId, verificationId) || other.verificationId == verificationId)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.resendToken, resendToken) || other.resendToken == resendToken));
}


@override
int get hashCode => Object.hash(runtimeType,verificationId,phoneNumber,resendToken);

@override
String toString() {
  return 'PhoneAuthState.codeSent(verificationId: $verificationId, phoneNumber: $phoneNumber, resendToken: $resendToken)';
}


}

/// @nodoc
abstract mixin class _$PhoneAuthCodeSentCopyWith<$Res> implements $PhoneAuthStateCopyWith<$Res> {
  factory _$PhoneAuthCodeSentCopyWith(_PhoneAuthCodeSent value, $Res Function(_PhoneAuthCodeSent) _then) = __$PhoneAuthCodeSentCopyWithImpl;
@useResult
$Res call({
 String verificationId, String phoneNumber, int? resendToken
});




}
/// @nodoc
class __$PhoneAuthCodeSentCopyWithImpl<$Res>
    implements _$PhoneAuthCodeSentCopyWith<$Res> {
  __$PhoneAuthCodeSentCopyWithImpl(this._self, this._then);

  final _PhoneAuthCodeSent _self;
  final $Res Function(_PhoneAuthCodeSent) _then;

/// Create a copy of PhoneAuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? verificationId = null,Object? phoneNumber = null,Object? resendToken = freezed,}) {
  return _then(_PhoneAuthCodeSent(
verificationId: null == verificationId ? _self.verificationId : verificationId // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,resendToken: freezed == resendToken ? _self.resendToken : resendToken // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

/// @nodoc


class _PhoneAuthCodeVerified implements PhoneAuthState {
  const _PhoneAuthCodeVerified({required this.phoneNumber});
  

 final  String phoneNumber;

/// Create a copy of PhoneAuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PhoneAuthCodeVerifiedCopyWith<_PhoneAuthCodeVerified> get copyWith => __$PhoneAuthCodeVerifiedCopyWithImpl<_PhoneAuthCodeVerified>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PhoneAuthCodeVerified&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber));
}


@override
int get hashCode => Object.hash(runtimeType,phoneNumber);

@override
String toString() {
  return 'PhoneAuthState.codeVerified(phoneNumber: $phoneNumber)';
}


}

/// @nodoc
abstract mixin class _$PhoneAuthCodeVerifiedCopyWith<$Res> implements $PhoneAuthStateCopyWith<$Res> {
  factory _$PhoneAuthCodeVerifiedCopyWith(_PhoneAuthCodeVerified value, $Res Function(_PhoneAuthCodeVerified) _then) = __$PhoneAuthCodeVerifiedCopyWithImpl;
@useResult
$Res call({
 String phoneNumber
});




}
/// @nodoc
class __$PhoneAuthCodeVerifiedCopyWithImpl<$Res>
    implements _$PhoneAuthCodeVerifiedCopyWith<$Res> {
  __$PhoneAuthCodeVerifiedCopyWithImpl(this._self, this._then);

  final _PhoneAuthCodeVerified _self;
  final $Res Function(_PhoneAuthCodeVerified) _then;

/// Create a copy of PhoneAuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? phoneNumber = null,}) {
  return _then(_PhoneAuthCodeVerified(
phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _PhoneAuthError implements PhoneAuthState {
  const _PhoneAuthError({required this.message, this.errorCode});
  

 final  String message;
 final  String? errorCode;

/// Create a copy of PhoneAuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PhoneAuthErrorCopyWith<_PhoneAuthError> get copyWith => __$PhoneAuthErrorCopyWithImpl<_PhoneAuthError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PhoneAuthError&&(identical(other.message, message) || other.message == message)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode));
}


@override
int get hashCode => Object.hash(runtimeType,message,errorCode);

@override
String toString() {
  return 'PhoneAuthState.error(message: $message, errorCode: $errorCode)';
}


}

/// @nodoc
abstract mixin class _$PhoneAuthErrorCopyWith<$Res> implements $PhoneAuthStateCopyWith<$Res> {
  factory _$PhoneAuthErrorCopyWith(_PhoneAuthError value, $Res Function(_PhoneAuthError) _then) = __$PhoneAuthErrorCopyWithImpl;
@useResult
$Res call({
 String message, String? errorCode
});




}
/// @nodoc
class __$PhoneAuthErrorCopyWithImpl<$Res>
    implements _$PhoneAuthErrorCopyWith<$Res> {
  __$PhoneAuthErrorCopyWithImpl(this._self, this._then);

  final _PhoneAuthError _self;
  final $Res Function(_PhoneAuthError) _then;

/// Create a copy of PhoneAuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,Object? errorCode = freezed,}) {
  return _then(_PhoneAuthError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _PhoneAuthLoading implements PhoneAuthState {
  const _PhoneAuthLoading({this.message});
  

 final  String? message;

/// Create a copy of PhoneAuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PhoneAuthLoadingCopyWith<_PhoneAuthLoading> get copyWith => __$PhoneAuthLoadingCopyWithImpl<_PhoneAuthLoading>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PhoneAuthLoading&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'PhoneAuthState.loading(message: $message)';
}


}

/// @nodoc
abstract mixin class _$PhoneAuthLoadingCopyWith<$Res> implements $PhoneAuthStateCopyWith<$Res> {
  factory _$PhoneAuthLoadingCopyWith(_PhoneAuthLoading value, $Res Function(_PhoneAuthLoading) _then) = __$PhoneAuthLoadingCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class __$PhoneAuthLoadingCopyWithImpl<$Res>
    implements _$PhoneAuthLoadingCopyWith<$Res> {
  __$PhoneAuthLoadingCopyWithImpl(this._self, this._then);

  final _PhoneAuthLoading _self;
  final $Res Function(_PhoneAuthLoading) _then;

/// Create a copy of PhoneAuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(_PhoneAuthLoading(
message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc
mixin _$BiometricAuthState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BiometricAuthState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'BiometricAuthState()';
}


}

/// @nodoc
class $BiometricAuthStateCopyWith<$Res>  {
$BiometricAuthStateCopyWith(BiometricAuthState _, $Res Function(BiometricAuthState) __);
}


/// Adds pattern-matching-related methods to [BiometricAuthState].
extension BiometricAuthStatePatterns on BiometricAuthState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _BiometricAuthInitial value)?  initial,TResult Function( _BiometricAuthAvailable value)?  available,TResult Function( _BiometricAuthAuthenticated value)?  authenticated,TResult Function( _BiometricAuthError value)?  error,TResult Function( _BiometricAuthUnavailable value)?  unavailable,required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BiometricAuthInitial() when initial != null:
return initial(_that);case _BiometricAuthAvailable() when available != null:
return available(_that);case _BiometricAuthAuthenticated() when authenticated != null:
return authenticated(_that);case _BiometricAuthError() when error != null:
return error(_that);case _BiometricAuthUnavailable() when unavailable != null:
return unavailable(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _BiometricAuthInitial value)  initial,required TResult Function( _BiometricAuthAvailable value)  available,required TResult Function( _BiometricAuthAuthenticated value)  authenticated,required TResult Function( _BiometricAuthError value)  error,required TResult Function( _BiometricAuthUnavailable value)  unavailable,}){
final _that = this;
switch (_that) {
case _BiometricAuthInitial():
return initial(_that);case _BiometricAuthAvailable():
return available(_that);case _BiometricAuthAuthenticated():
return authenticated(_that);case _BiometricAuthError():
return error(_that);case _BiometricAuthUnavailable():
return unavailable(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _BiometricAuthInitial value)?  initial,TResult? Function( _BiometricAuthAvailable value)?  available,TResult? Function( _BiometricAuthAuthenticated value)?  authenticated,TResult? Function( _BiometricAuthError value)?  error,TResult? Function( _BiometricAuthUnavailable value)?  unavailable,}){
final _that = this;
switch (_that) {
case _BiometricAuthInitial() when initial != null:
return initial(_that);case _BiometricAuthAvailable() when available != null:
return available(_that);case _BiometricAuthAuthenticated() when authenticated != null:
return authenticated(_that);case _BiometricAuthError() when error != null:
return error(_that);case _BiometricAuthUnavailable() when unavailable != null:
return unavailable(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  initial,TResult Function( List<BiometricType> availableBiometrics,  bool isDeviceSupported)?  available,TResult Function()?  authenticated,TResult Function( String message)?  error,TResult Function()?  unavailable,required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BiometricAuthInitial() when initial != null:
return initial();case _BiometricAuthAvailable() when available != null:
return available(_that.availableBiometrics,_that.isDeviceSupported);case _BiometricAuthAuthenticated() when authenticated != null:
return authenticated();case _BiometricAuthError() when error != null:
return error(_that.message);case _BiometricAuthUnavailable() when unavailable != null:
return unavailable();case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  initial,required TResult Function( List<BiometricType> availableBiometrics,  bool isDeviceSupported)  available,required TResult Function()  authenticated,required TResult Function( String message)  error,required TResult Function()  unavailable,}) {final _that = this;
switch (_that) {
case _BiometricAuthInitial():
return initial();case _BiometricAuthAvailable():
return available(_that.availableBiometrics,_that.isDeviceSupported);case _BiometricAuthAuthenticated():
return authenticated();case _BiometricAuthError():
return error(_that.message);case _BiometricAuthUnavailable():
return unavailable();case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  initial,TResult? Function( List<BiometricType> availableBiometrics,  bool isDeviceSupported)?  available,TResult? Function()?  authenticated,TResult? Function( String message)?  error,TResult? Function()?  unavailable,}) {final _that = this;
switch (_that) {
case _BiometricAuthInitial() when initial != null:
return initial();case _BiometricAuthAvailable() when available != null:
return available(_that.availableBiometrics,_that.isDeviceSupported);case _BiometricAuthAuthenticated() when authenticated != null:
return authenticated();case _BiometricAuthError() when error != null:
return error(_that.message);case _BiometricAuthUnavailable() when unavailable != null:
return unavailable();case _:
  return null;

}
}

}

/// @nodoc


class _BiometricAuthInitial implements BiometricAuthState {
  const _BiometricAuthInitial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BiometricAuthInitial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'BiometricAuthState.initial()';
}


}




/// @nodoc


class _BiometricAuthAvailable implements BiometricAuthState {
  const _BiometricAuthAvailable({required final  List<BiometricType> availableBiometrics, required this.isDeviceSupported}): _availableBiometrics = availableBiometrics;
  

 final  List<BiometricType> _availableBiometrics;
 List<BiometricType> get availableBiometrics {
  if (_availableBiometrics is EqualUnmodifiableListView) return _availableBiometrics;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_availableBiometrics);
}

 final  bool isDeviceSupported;

/// Create a copy of BiometricAuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BiometricAuthAvailableCopyWith<_BiometricAuthAvailable> get copyWith => __$BiometricAuthAvailableCopyWithImpl<_BiometricAuthAvailable>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BiometricAuthAvailable&&const DeepCollectionEquality().equals(other._availableBiometrics, _availableBiometrics)&&(identical(other.isDeviceSupported, isDeviceSupported) || other.isDeviceSupported == isDeviceSupported));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_availableBiometrics),isDeviceSupported);

@override
String toString() {
  return 'BiometricAuthState.available(availableBiometrics: $availableBiometrics, isDeviceSupported: $isDeviceSupported)';
}


}

/// @nodoc
abstract mixin class _$BiometricAuthAvailableCopyWith<$Res> implements $BiometricAuthStateCopyWith<$Res> {
  factory _$BiometricAuthAvailableCopyWith(_BiometricAuthAvailable value, $Res Function(_BiometricAuthAvailable) _then) = __$BiometricAuthAvailableCopyWithImpl;
@useResult
$Res call({
 List<BiometricType> availableBiometrics, bool isDeviceSupported
});




}
/// @nodoc
class __$BiometricAuthAvailableCopyWithImpl<$Res>
    implements _$BiometricAuthAvailableCopyWith<$Res> {
  __$BiometricAuthAvailableCopyWithImpl(this._self, this._then);

  final _BiometricAuthAvailable _self;
  final $Res Function(_BiometricAuthAvailable) _then;

/// Create a copy of BiometricAuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? availableBiometrics = null,Object? isDeviceSupported = null,}) {
  return _then(_BiometricAuthAvailable(
availableBiometrics: null == availableBiometrics ? _self._availableBiometrics : availableBiometrics // ignore: cast_nullable_to_non_nullable
as List<BiometricType>,isDeviceSupported: null == isDeviceSupported ? _self.isDeviceSupported : isDeviceSupported // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class _BiometricAuthAuthenticated implements BiometricAuthState {
  const _BiometricAuthAuthenticated();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BiometricAuthAuthenticated);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'BiometricAuthState.authenticated()';
}


}




/// @nodoc


class _BiometricAuthError implements BiometricAuthState {
  const _BiometricAuthError({required this.message});
  

 final  String message;

/// Create a copy of BiometricAuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BiometricAuthErrorCopyWith<_BiometricAuthError> get copyWith => __$BiometricAuthErrorCopyWithImpl<_BiometricAuthError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BiometricAuthError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'BiometricAuthState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class _$BiometricAuthErrorCopyWith<$Res> implements $BiometricAuthStateCopyWith<$Res> {
  factory _$BiometricAuthErrorCopyWith(_BiometricAuthError value, $Res Function(_BiometricAuthError) _then) = __$BiometricAuthErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class __$BiometricAuthErrorCopyWithImpl<$Res>
    implements _$BiometricAuthErrorCopyWith<$Res> {
  __$BiometricAuthErrorCopyWithImpl(this._self, this._then);

  final _BiometricAuthError _self;
  final $Res Function(_BiometricAuthError) _then;

/// Create a copy of BiometricAuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(_BiometricAuthError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _BiometricAuthUnavailable implements BiometricAuthState {
  const _BiometricAuthUnavailable();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BiometricAuthUnavailable);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'BiometricAuthState.unavailable()';
}


}





/// @nodoc
mixin _$AuthConfiguration {

 bool get enableEmailAuth; bool get enableGoogleAuth; bool get enableAppleAuth; bool get enablePhoneAuth; bool get enableAnonymousAuth; bool get enableBiometricAuth; bool get requireEmailVerification; bool get enablePasswordReset; int get phoneCodeExpirationMinutes; int get maxPasswordResetAttempts;
/// Create a copy of AuthConfiguration
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthConfigurationCopyWith<AuthConfiguration> get copyWith => _$AuthConfigurationCopyWithImpl<AuthConfiguration>(this as AuthConfiguration, _$identity);

  /// Serializes this AuthConfiguration to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthConfiguration&&(identical(other.enableEmailAuth, enableEmailAuth) || other.enableEmailAuth == enableEmailAuth)&&(identical(other.enableGoogleAuth, enableGoogleAuth) || other.enableGoogleAuth == enableGoogleAuth)&&(identical(other.enableAppleAuth, enableAppleAuth) || other.enableAppleAuth == enableAppleAuth)&&(identical(other.enablePhoneAuth, enablePhoneAuth) || other.enablePhoneAuth == enablePhoneAuth)&&(identical(other.enableAnonymousAuth, enableAnonymousAuth) || other.enableAnonymousAuth == enableAnonymousAuth)&&(identical(other.enableBiometricAuth, enableBiometricAuth) || other.enableBiometricAuth == enableBiometricAuth)&&(identical(other.requireEmailVerification, requireEmailVerification) || other.requireEmailVerification == requireEmailVerification)&&(identical(other.enablePasswordReset, enablePasswordReset) || other.enablePasswordReset == enablePasswordReset)&&(identical(other.phoneCodeExpirationMinutes, phoneCodeExpirationMinutes) || other.phoneCodeExpirationMinutes == phoneCodeExpirationMinutes)&&(identical(other.maxPasswordResetAttempts, maxPasswordResetAttempts) || other.maxPasswordResetAttempts == maxPasswordResetAttempts));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,enableEmailAuth,enableGoogleAuth,enableAppleAuth,enablePhoneAuth,enableAnonymousAuth,enableBiometricAuth,requireEmailVerification,enablePasswordReset,phoneCodeExpirationMinutes,maxPasswordResetAttempts);

@override
String toString() {
  return 'AuthConfiguration(enableEmailAuth: $enableEmailAuth, enableGoogleAuth: $enableGoogleAuth, enableAppleAuth: $enableAppleAuth, enablePhoneAuth: $enablePhoneAuth, enableAnonymousAuth: $enableAnonymousAuth, enableBiometricAuth: $enableBiometricAuth, requireEmailVerification: $requireEmailVerification, enablePasswordReset: $enablePasswordReset, phoneCodeExpirationMinutes: $phoneCodeExpirationMinutes, maxPasswordResetAttempts: $maxPasswordResetAttempts)';
}


}

/// @nodoc
abstract mixin class $AuthConfigurationCopyWith<$Res>  {
  factory $AuthConfigurationCopyWith(AuthConfiguration value, $Res Function(AuthConfiguration) _then) = _$AuthConfigurationCopyWithImpl;
@useResult
$Res call({
 bool enableEmailAuth, bool enableGoogleAuth, bool enableAppleAuth, bool enablePhoneAuth, bool enableAnonymousAuth, bool enableBiometricAuth, bool requireEmailVerification, bool enablePasswordReset, int phoneCodeExpirationMinutes, int maxPasswordResetAttempts
});




}
/// @nodoc
class _$AuthConfigurationCopyWithImpl<$Res>
    implements $AuthConfigurationCopyWith<$Res> {
  _$AuthConfigurationCopyWithImpl(this._self, this._then);

  final AuthConfiguration _self;
  final $Res Function(AuthConfiguration) _then;

/// Create a copy of AuthConfiguration
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? enableEmailAuth = null,Object? enableGoogleAuth = null,Object? enableAppleAuth = null,Object? enablePhoneAuth = null,Object? enableAnonymousAuth = null,Object? enableBiometricAuth = null,Object? requireEmailVerification = null,Object? enablePasswordReset = null,Object? phoneCodeExpirationMinutes = null,Object? maxPasswordResetAttempts = null,}) {
  return _then(_self.copyWith(
enableEmailAuth: null == enableEmailAuth ? _self.enableEmailAuth : enableEmailAuth // ignore: cast_nullable_to_non_nullable
as bool,enableGoogleAuth: null == enableGoogleAuth ? _self.enableGoogleAuth : enableGoogleAuth // ignore: cast_nullable_to_non_nullable
as bool,enableAppleAuth: null == enableAppleAuth ? _self.enableAppleAuth : enableAppleAuth // ignore: cast_nullable_to_non_nullable
as bool,enablePhoneAuth: null == enablePhoneAuth ? _self.enablePhoneAuth : enablePhoneAuth // ignore: cast_nullable_to_non_nullable
as bool,enableAnonymousAuth: null == enableAnonymousAuth ? _self.enableAnonymousAuth : enableAnonymousAuth // ignore: cast_nullable_to_non_nullable
as bool,enableBiometricAuth: null == enableBiometricAuth ? _self.enableBiometricAuth : enableBiometricAuth // ignore: cast_nullable_to_non_nullable
as bool,requireEmailVerification: null == requireEmailVerification ? _self.requireEmailVerification : requireEmailVerification // ignore: cast_nullable_to_non_nullable
as bool,enablePasswordReset: null == enablePasswordReset ? _self.enablePasswordReset : enablePasswordReset // ignore: cast_nullable_to_non_nullable
as bool,phoneCodeExpirationMinutes: null == phoneCodeExpirationMinutes ? _self.phoneCodeExpirationMinutes : phoneCodeExpirationMinutes // ignore: cast_nullable_to_non_nullable
as int,maxPasswordResetAttempts: null == maxPasswordResetAttempts ? _self.maxPasswordResetAttempts : maxPasswordResetAttempts // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [AuthConfiguration].
extension AuthConfigurationPatterns on AuthConfiguration {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AuthConfiguration value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AuthConfiguration() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AuthConfiguration value)  $default,){
final _that = this;
switch (_that) {
case _AuthConfiguration():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AuthConfiguration value)?  $default,){
final _that = this;
switch (_that) {
case _AuthConfiguration() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool enableEmailAuth,  bool enableGoogleAuth,  bool enableAppleAuth,  bool enablePhoneAuth,  bool enableAnonymousAuth,  bool enableBiometricAuth,  bool requireEmailVerification,  bool enablePasswordReset,  int phoneCodeExpirationMinutes,  int maxPasswordResetAttempts)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AuthConfiguration() when $default != null:
return $default(_that.enableEmailAuth,_that.enableGoogleAuth,_that.enableAppleAuth,_that.enablePhoneAuth,_that.enableAnonymousAuth,_that.enableBiometricAuth,_that.requireEmailVerification,_that.enablePasswordReset,_that.phoneCodeExpirationMinutes,_that.maxPasswordResetAttempts);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool enableEmailAuth,  bool enableGoogleAuth,  bool enableAppleAuth,  bool enablePhoneAuth,  bool enableAnonymousAuth,  bool enableBiometricAuth,  bool requireEmailVerification,  bool enablePasswordReset,  int phoneCodeExpirationMinutes,  int maxPasswordResetAttempts)  $default,) {final _that = this;
switch (_that) {
case _AuthConfiguration():
return $default(_that.enableEmailAuth,_that.enableGoogleAuth,_that.enableAppleAuth,_that.enablePhoneAuth,_that.enableAnonymousAuth,_that.enableBiometricAuth,_that.requireEmailVerification,_that.enablePasswordReset,_that.phoneCodeExpirationMinutes,_that.maxPasswordResetAttempts);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool enableEmailAuth,  bool enableGoogleAuth,  bool enableAppleAuth,  bool enablePhoneAuth,  bool enableAnonymousAuth,  bool enableBiometricAuth,  bool requireEmailVerification,  bool enablePasswordReset,  int phoneCodeExpirationMinutes,  int maxPasswordResetAttempts)?  $default,) {final _that = this;
switch (_that) {
case _AuthConfiguration() when $default != null:
return $default(_that.enableEmailAuth,_that.enableGoogleAuth,_that.enableAppleAuth,_that.enablePhoneAuth,_that.enableAnonymousAuth,_that.enableBiometricAuth,_that.requireEmailVerification,_that.enablePasswordReset,_that.phoneCodeExpirationMinutes,_that.maxPasswordResetAttempts);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AuthConfiguration implements AuthConfiguration {
  const _AuthConfiguration({this.enableEmailAuth = true, this.enableGoogleAuth = true, this.enableAppleAuth = true, this.enablePhoneAuth = true, this.enableAnonymousAuth = false, this.enableBiometricAuth = true, this.requireEmailVerification = false, this.enablePasswordReset = true, this.phoneCodeExpirationMinutes = 30, this.maxPasswordResetAttempts = 3});
  factory _AuthConfiguration.fromJson(Map<String, dynamic> json) => _$AuthConfigurationFromJson(json);

@override@JsonKey() final  bool enableEmailAuth;
@override@JsonKey() final  bool enableGoogleAuth;
@override@JsonKey() final  bool enableAppleAuth;
@override@JsonKey() final  bool enablePhoneAuth;
@override@JsonKey() final  bool enableAnonymousAuth;
@override@JsonKey() final  bool enableBiometricAuth;
@override@JsonKey() final  bool requireEmailVerification;
@override@JsonKey() final  bool enablePasswordReset;
@override@JsonKey() final  int phoneCodeExpirationMinutes;
@override@JsonKey() final  int maxPasswordResetAttempts;

/// Create a copy of AuthConfiguration
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AuthConfigurationCopyWith<_AuthConfiguration> get copyWith => __$AuthConfigurationCopyWithImpl<_AuthConfiguration>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AuthConfigurationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AuthConfiguration&&(identical(other.enableEmailAuth, enableEmailAuth) || other.enableEmailAuth == enableEmailAuth)&&(identical(other.enableGoogleAuth, enableGoogleAuth) || other.enableGoogleAuth == enableGoogleAuth)&&(identical(other.enableAppleAuth, enableAppleAuth) || other.enableAppleAuth == enableAppleAuth)&&(identical(other.enablePhoneAuth, enablePhoneAuth) || other.enablePhoneAuth == enablePhoneAuth)&&(identical(other.enableAnonymousAuth, enableAnonymousAuth) || other.enableAnonymousAuth == enableAnonymousAuth)&&(identical(other.enableBiometricAuth, enableBiometricAuth) || other.enableBiometricAuth == enableBiometricAuth)&&(identical(other.requireEmailVerification, requireEmailVerification) || other.requireEmailVerification == requireEmailVerification)&&(identical(other.enablePasswordReset, enablePasswordReset) || other.enablePasswordReset == enablePasswordReset)&&(identical(other.phoneCodeExpirationMinutes, phoneCodeExpirationMinutes) || other.phoneCodeExpirationMinutes == phoneCodeExpirationMinutes)&&(identical(other.maxPasswordResetAttempts, maxPasswordResetAttempts) || other.maxPasswordResetAttempts == maxPasswordResetAttempts));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,enableEmailAuth,enableGoogleAuth,enableAppleAuth,enablePhoneAuth,enableAnonymousAuth,enableBiometricAuth,requireEmailVerification,enablePasswordReset,phoneCodeExpirationMinutes,maxPasswordResetAttempts);

@override
String toString() {
  return 'AuthConfiguration(enableEmailAuth: $enableEmailAuth, enableGoogleAuth: $enableGoogleAuth, enableAppleAuth: $enableAppleAuth, enablePhoneAuth: $enablePhoneAuth, enableAnonymousAuth: $enableAnonymousAuth, enableBiometricAuth: $enableBiometricAuth, requireEmailVerification: $requireEmailVerification, enablePasswordReset: $enablePasswordReset, phoneCodeExpirationMinutes: $phoneCodeExpirationMinutes, maxPasswordResetAttempts: $maxPasswordResetAttempts)';
}


}

/// @nodoc
abstract mixin class _$AuthConfigurationCopyWith<$Res> implements $AuthConfigurationCopyWith<$Res> {
  factory _$AuthConfigurationCopyWith(_AuthConfiguration value, $Res Function(_AuthConfiguration) _then) = __$AuthConfigurationCopyWithImpl;
@override @useResult
$Res call({
 bool enableEmailAuth, bool enableGoogleAuth, bool enableAppleAuth, bool enablePhoneAuth, bool enableAnonymousAuth, bool enableBiometricAuth, bool requireEmailVerification, bool enablePasswordReset, int phoneCodeExpirationMinutes, int maxPasswordResetAttempts
});




}
/// @nodoc
class __$AuthConfigurationCopyWithImpl<$Res>
    implements _$AuthConfigurationCopyWith<$Res> {
  __$AuthConfigurationCopyWithImpl(this._self, this._then);

  final _AuthConfiguration _self;
  final $Res Function(_AuthConfiguration) _then;

/// Create a copy of AuthConfiguration
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? enableEmailAuth = null,Object? enableGoogleAuth = null,Object? enableAppleAuth = null,Object? enablePhoneAuth = null,Object? enableAnonymousAuth = null,Object? enableBiometricAuth = null,Object? requireEmailVerification = null,Object? enablePasswordReset = null,Object? phoneCodeExpirationMinutes = null,Object? maxPasswordResetAttempts = null,}) {
  return _then(_AuthConfiguration(
enableEmailAuth: null == enableEmailAuth ? _self.enableEmailAuth : enableEmailAuth // ignore: cast_nullable_to_non_nullable
as bool,enableGoogleAuth: null == enableGoogleAuth ? _self.enableGoogleAuth : enableGoogleAuth // ignore: cast_nullable_to_non_nullable
as bool,enableAppleAuth: null == enableAppleAuth ? _self.enableAppleAuth : enableAppleAuth // ignore: cast_nullable_to_non_nullable
as bool,enablePhoneAuth: null == enablePhoneAuth ? _self.enablePhoneAuth : enablePhoneAuth // ignore: cast_nullable_to_non_nullable
as bool,enableAnonymousAuth: null == enableAnonymousAuth ? _self.enableAnonymousAuth : enableAnonymousAuth // ignore: cast_nullable_to_non_nullable
as bool,enableBiometricAuth: null == enableBiometricAuth ? _self.enableBiometricAuth : enableBiometricAuth // ignore: cast_nullable_to_non_nullable
as bool,requireEmailVerification: null == requireEmailVerification ? _self.requireEmailVerification : requireEmailVerification // ignore: cast_nullable_to_non_nullable
as bool,enablePasswordReset: null == enablePasswordReset ? _self.enablePasswordReset : enablePasswordReset // ignore: cast_nullable_to_non_nullable
as bool,phoneCodeExpirationMinutes: null == phoneCodeExpirationMinutes ? _self.phoneCodeExpirationMinutes : phoneCodeExpirationMinutes // ignore: cast_nullable_to_non_nullable
as int,maxPasswordResetAttempts: null == maxPasswordResetAttempts ? _self.maxPasswordResetAttempts : maxPasswordResetAttempts // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
