// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => _UserProfile(
  id: json['id'] as String,
  email: json['email'] as String,
  fullName: json['fullName'] as String?,
  avatarUrl: json['avatarUrl'] as String?,
  phone: json['phone'] as String?,
  isOnboarded: json['isOnboarded'] as bool? ?? false,
  subscriptionTier: json['subscriptionTier'] as String? ?? 'free',
  createdAt:
      json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
  updatedAt:
      json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
  preferences: json['preferences'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$UserProfileToJson(_UserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'fullName': instance.fullName,
      'avatarUrl': instance.avatarUrl,
      'phone': instance.phone,
      'isOnboarded': instance.isOnboarded,
      'subscriptionTier': instance.subscriptionTier,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'preferences': instance.preferences,
    };

_OnboardingStep _$OnboardingStepFromJson(Map<String, dynamic> json) =>
    _OnboardingStep(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      icon: json['icon'] as String,
      isCompleted: json['isCompleted'] as bool? ?? false,
    );

Map<String, dynamic> _$OnboardingStepToJson(_OnboardingStep instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'icon': instance.icon,
      'isCompleted': instance.isCompleted,
    };

_UserPreferences _$UserPreferencesFromJson(Map<String, dynamic> json) =>
    _UserPreferences(
      pushNotifications: json['pushNotifications'] as bool? ?? true,
      emailNotifications: json['emailNotifications'] as bool? ?? true,
      themeMode: json['themeMode'] as String? ?? 'light',
      currency: json['currency'] as String? ?? 'USD',
      market: json['market'] as String? ?? 'US',
      watchlist:
          (json['watchlist'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      aiAssistantSettings:
          json['aiAssistantSettings'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$UserPreferencesToJson(_UserPreferences instance) =>
    <String, dynamic>{
      'pushNotifications': instance.pushNotifications,
      'emailNotifications': instance.emailNotifications,
      'themeMode': instance.themeMode,
      'currency': instance.currency,
      'market': instance.market,
      'watchlist': instance.watchlist,
      'aiAssistantSettings': instance.aiAssistantSettings,
    };
