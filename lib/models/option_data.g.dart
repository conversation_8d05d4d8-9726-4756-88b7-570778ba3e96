// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'option_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_OptionData _$OptionDataFromJson(Map<String, dynamic> json) => _OptionData(
  timestamp: json['timestamp'] as String,
  expiryDate: json['expiryDate'] as String,
  strikePrice: (json['strikePrice'] as num).toDouble(),
  optionType: json['optionType'] as String,
  spotPrice: (json['spotPrice'] as num).toDouble(),
  lastPrice: (json['lastPrice'] as num).toDouble(),
  previousClosePrice: (json['previousClosePrice'] as num).toDouble(),
  volume: (json['volume'] as num).toInt(),
  previousVolume: (json['previousVolume'] as num).toInt(),
  oi: (json['oi'] as num).toInt(),
  previousOi: (json['previousOi'] as num).toInt(),
  topBidPrice: (json['topBidPrice'] as num).toDouble(),
  topAskPrice: (json['topAskPrice'] as num).toDouble(),
  topBidQuantity: (json['topBidQuantity'] as num).toInt(),
  topAskQuantity: (json['topAskQuantity'] as num).toInt(),
  iv: (json['iv'] as num).toDouble(),
  delta: (json['delta'] as num).toDouble(),
  gamma: (json['gamma'] as num).toDouble(),
  theta: (json['theta'] as num).toDouble(),
  vega: (json['vega'] as num).toDouble(),
);

Map<String, dynamic> _$OptionDataToJson(_OptionData instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp,
      'expiryDate': instance.expiryDate,
      'strikePrice': instance.strikePrice,
      'optionType': instance.optionType,
      'spotPrice': instance.spotPrice,
      'lastPrice': instance.lastPrice,
      'previousClosePrice': instance.previousClosePrice,
      'volume': instance.volume,
      'previousVolume': instance.previousVolume,
      'oi': instance.oi,
      'previousOi': instance.previousOi,
      'topBidPrice': instance.topBidPrice,
      'topAskPrice': instance.topAskPrice,
      'topBidQuantity': instance.topBidQuantity,
      'topAskQuantity': instance.topAskQuantity,
      'iv': instance.iv,
      'delta': instance.delta,
      'gamma': instance.gamma,
      'theta': instance.theta,
      'vega': instance.vega,
    };
