// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'option_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OptionData {

 String get timestamp; String get expiryDate; double get strikePrice; String get optionType; double get spotPrice; double get lastPrice; double get previousClosePrice; int get volume; int get previousVolume; int get oi; int get previousOi; double get topBidPrice; double get topAskPrice; int get topBidQuantity; int get topAskQuantity; double get iv; double get delta; double get gamma; double get theta; double get vega;
/// Create a copy of OptionData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OptionDataCopyWith<OptionData> get copyWith => _$OptionDataCopyWithImpl<OptionData>(this as OptionData, _$identity);

  /// Serializes this OptionData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OptionData&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&(identical(other.strikePrice, strikePrice) || other.strikePrice == strikePrice)&&(identical(other.optionType, optionType) || other.optionType == optionType)&&(identical(other.spotPrice, spotPrice) || other.spotPrice == spotPrice)&&(identical(other.lastPrice, lastPrice) || other.lastPrice == lastPrice)&&(identical(other.previousClosePrice, previousClosePrice) || other.previousClosePrice == previousClosePrice)&&(identical(other.volume, volume) || other.volume == volume)&&(identical(other.previousVolume, previousVolume) || other.previousVolume == previousVolume)&&(identical(other.oi, oi) || other.oi == oi)&&(identical(other.previousOi, previousOi) || other.previousOi == previousOi)&&(identical(other.topBidPrice, topBidPrice) || other.topBidPrice == topBidPrice)&&(identical(other.topAskPrice, topAskPrice) || other.topAskPrice == topAskPrice)&&(identical(other.topBidQuantity, topBidQuantity) || other.topBidQuantity == topBidQuantity)&&(identical(other.topAskQuantity, topAskQuantity) || other.topAskQuantity == topAskQuantity)&&(identical(other.iv, iv) || other.iv == iv)&&(identical(other.delta, delta) || other.delta == delta)&&(identical(other.gamma, gamma) || other.gamma == gamma)&&(identical(other.theta, theta) || other.theta == theta)&&(identical(other.vega, vega) || other.vega == vega));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,timestamp,expiryDate,strikePrice,optionType,spotPrice,lastPrice,previousClosePrice,volume,previousVolume,oi,previousOi,topBidPrice,topAskPrice,topBidQuantity,topAskQuantity,iv,delta,gamma,theta,vega]);

@override
String toString() {
  return 'OptionData(timestamp: $timestamp, expiryDate: $expiryDate, strikePrice: $strikePrice, optionType: $optionType, spotPrice: $spotPrice, lastPrice: $lastPrice, previousClosePrice: $previousClosePrice, volume: $volume, previousVolume: $previousVolume, oi: $oi, previousOi: $previousOi, topBidPrice: $topBidPrice, topAskPrice: $topAskPrice, topBidQuantity: $topBidQuantity, topAskQuantity: $topAskQuantity, iv: $iv, delta: $delta, gamma: $gamma, theta: $theta, vega: $vega)';
}


}

/// @nodoc
abstract mixin class $OptionDataCopyWith<$Res>  {
  factory $OptionDataCopyWith(OptionData value, $Res Function(OptionData) _then) = _$OptionDataCopyWithImpl;
@useResult
$Res call({
 String timestamp, String expiryDate, double strikePrice, String optionType, double spotPrice, double lastPrice, double previousClosePrice, int volume, int previousVolume, int oi, int previousOi, double topBidPrice, double topAskPrice, int topBidQuantity, int topAskQuantity, double iv, double delta, double gamma, double theta, double vega
});




}
/// @nodoc
class _$OptionDataCopyWithImpl<$Res>
    implements $OptionDataCopyWith<$Res> {
  _$OptionDataCopyWithImpl(this._self, this._then);

  final OptionData _self;
  final $Res Function(OptionData) _then;

/// Create a copy of OptionData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? timestamp = null,Object? expiryDate = null,Object? strikePrice = null,Object? optionType = null,Object? spotPrice = null,Object? lastPrice = null,Object? previousClosePrice = null,Object? volume = null,Object? previousVolume = null,Object? oi = null,Object? previousOi = null,Object? topBidPrice = null,Object? topAskPrice = null,Object? topBidQuantity = null,Object? topAskQuantity = null,Object? iv = null,Object? delta = null,Object? gamma = null,Object? theta = null,Object? vega = null,}) {
  return _then(_self.copyWith(
timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as String,expiryDate: null == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as String,strikePrice: null == strikePrice ? _self.strikePrice : strikePrice // ignore: cast_nullable_to_non_nullable
as double,optionType: null == optionType ? _self.optionType : optionType // ignore: cast_nullable_to_non_nullable
as String,spotPrice: null == spotPrice ? _self.spotPrice : spotPrice // ignore: cast_nullable_to_non_nullable
as double,lastPrice: null == lastPrice ? _self.lastPrice : lastPrice // ignore: cast_nullable_to_non_nullable
as double,previousClosePrice: null == previousClosePrice ? _self.previousClosePrice : previousClosePrice // ignore: cast_nullable_to_non_nullable
as double,volume: null == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as int,previousVolume: null == previousVolume ? _self.previousVolume : previousVolume // ignore: cast_nullable_to_non_nullable
as int,oi: null == oi ? _self.oi : oi // ignore: cast_nullable_to_non_nullable
as int,previousOi: null == previousOi ? _self.previousOi : previousOi // ignore: cast_nullable_to_non_nullable
as int,topBidPrice: null == topBidPrice ? _self.topBidPrice : topBidPrice // ignore: cast_nullable_to_non_nullable
as double,topAskPrice: null == topAskPrice ? _self.topAskPrice : topAskPrice // ignore: cast_nullable_to_non_nullable
as double,topBidQuantity: null == topBidQuantity ? _self.topBidQuantity : topBidQuantity // ignore: cast_nullable_to_non_nullable
as int,topAskQuantity: null == topAskQuantity ? _self.topAskQuantity : topAskQuantity // ignore: cast_nullable_to_non_nullable
as int,iv: null == iv ? _self.iv : iv // ignore: cast_nullable_to_non_nullable
as double,delta: null == delta ? _self.delta : delta // ignore: cast_nullable_to_non_nullable
as double,gamma: null == gamma ? _self.gamma : gamma // ignore: cast_nullable_to_non_nullable
as double,theta: null == theta ? _self.theta : theta // ignore: cast_nullable_to_non_nullable
as double,vega: null == vega ? _self.vega : vega // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [OptionData].
extension OptionDataPatterns on OptionData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OptionData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OptionData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OptionData value)  $default,){
final _that = this;
switch (_that) {
case _OptionData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OptionData value)?  $default,){
final _that = this;
switch (_that) {
case _OptionData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String timestamp,  String expiryDate,  double strikePrice,  String optionType,  double spotPrice,  double lastPrice,  double previousClosePrice,  int volume,  int previousVolume,  int oi,  int previousOi,  double topBidPrice,  double topAskPrice,  int topBidQuantity,  int topAskQuantity,  double iv,  double delta,  double gamma,  double theta,  double vega)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OptionData() when $default != null:
return $default(_that.timestamp,_that.expiryDate,_that.strikePrice,_that.optionType,_that.spotPrice,_that.lastPrice,_that.previousClosePrice,_that.volume,_that.previousVolume,_that.oi,_that.previousOi,_that.topBidPrice,_that.topAskPrice,_that.topBidQuantity,_that.topAskQuantity,_that.iv,_that.delta,_that.gamma,_that.theta,_that.vega);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String timestamp,  String expiryDate,  double strikePrice,  String optionType,  double spotPrice,  double lastPrice,  double previousClosePrice,  int volume,  int previousVolume,  int oi,  int previousOi,  double topBidPrice,  double topAskPrice,  int topBidQuantity,  int topAskQuantity,  double iv,  double delta,  double gamma,  double theta,  double vega)  $default,) {final _that = this;
switch (_that) {
case _OptionData():
return $default(_that.timestamp,_that.expiryDate,_that.strikePrice,_that.optionType,_that.spotPrice,_that.lastPrice,_that.previousClosePrice,_that.volume,_that.previousVolume,_that.oi,_that.previousOi,_that.topBidPrice,_that.topAskPrice,_that.topBidQuantity,_that.topAskQuantity,_that.iv,_that.delta,_that.gamma,_that.theta,_that.vega);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String timestamp,  String expiryDate,  double strikePrice,  String optionType,  double spotPrice,  double lastPrice,  double previousClosePrice,  int volume,  int previousVolume,  int oi,  int previousOi,  double topBidPrice,  double topAskPrice,  int topBidQuantity,  int topAskQuantity,  double iv,  double delta,  double gamma,  double theta,  double vega)?  $default,) {final _that = this;
switch (_that) {
case _OptionData() when $default != null:
return $default(_that.timestamp,_that.expiryDate,_that.strikePrice,_that.optionType,_that.spotPrice,_that.lastPrice,_that.previousClosePrice,_that.volume,_that.previousVolume,_that.oi,_that.previousOi,_that.topBidPrice,_that.topAskPrice,_that.topBidQuantity,_that.topAskQuantity,_that.iv,_that.delta,_that.gamma,_that.theta,_that.vega);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _OptionData implements OptionData {
  const _OptionData({required this.timestamp, required this.expiryDate, required this.strikePrice, required this.optionType, required this.spotPrice, required this.lastPrice, required this.previousClosePrice, required this.volume, required this.previousVolume, required this.oi, required this.previousOi, required this.topBidPrice, required this.topAskPrice, required this.topBidQuantity, required this.topAskQuantity, required this.iv, required this.delta, required this.gamma, required this.theta, required this.vega});
  factory _OptionData.fromJson(Map<String, dynamic> json) => _$OptionDataFromJson(json);

@override final  String timestamp;
@override final  String expiryDate;
@override final  double strikePrice;
@override final  String optionType;
@override final  double spotPrice;
@override final  double lastPrice;
@override final  double previousClosePrice;
@override final  int volume;
@override final  int previousVolume;
@override final  int oi;
@override final  int previousOi;
@override final  double topBidPrice;
@override final  double topAskPrice;
@override final  int topBidQuantity;
@override final  int topAskQuantity;
@override final  double iv;
@override final  double delta;
@override final  double gamma;
@override final  double theta;
@override final  double vega;

/// Create a copy of OptionData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OptionDataCopyWith<_OptionData> get copyWith => __$OptionDataCopyWithImpl<_OptionData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OptionDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OptionData&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&(identical(other.strikePrice, strikePrice) || other.strikePrice == strikePrice)&&(identical(other.optionType, optionType) || other.optionType == optionType)&&(identical(other.spotPrice, spotPrice) || other.spotPrice == spotPrice)&&(identical(other.lastPrice, lastPrice) || other.lastPrice == lastPrice)&&(identical(other.previousClosePrice, previousClosePrice) || other.previousClosePrice == previousClosePrice)&&(identical(other.volume, volume) || other.volume == volume)&&(identical(other.previousVolume, previousVolume) || other.previousVolume == previousVolume)&&(identical(other.oi, oi) || other.oi == oi)&&(identical(other.previousOi, previousOi) || other.previousOi == previousOi)&&(identical(other.topBidPrice, topBidPrice) || other.topBidPrice == topBidPrice)&&(identical(other.topAskPrice, topAskPrice) || other.topAskPrice == topAskPrice)&&(identical(other.topBidQuantity, topBidQuantity) || other.topBidQuantity == topBidQuantity)&&(identical(other.topAskQuantity, topAskQuantity) || other.topAskQuantity == topAskQuantity)&&(identical(other.iv, iv) || other.iv == iv)&&(identical(other.delta, delta) || other.delta == delta)&&(identical(other.gamma, gamma) || other.gamma == gamma)&&(identical(other.theta, theta) || other.theta == theta)&&(identical(other.vega, vega) || other.vega == vega));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,timestamp,expiryDate,strikePrice,optionType,spotPrice,lastPrice,previousClosePrice,volume,previousVolume,oi,previousOi,topBidPrice,topAskPrice,topBidQuantity,topAskQuantity,iv,delta,gamma,theta,vega]);

@override
String toString() {
  return 'OptionData(timestamp: $timestamp, expiryDate: $expiryDate, strikePrice: $strikePrice, optionType: $optionType, spotPrice: $spotPrice, lastPrice: $lastPrice, previousClosePrice: $previousClosePrice, volume: $volume, previousVolume: $previousVolume, oi: $oi, previousOi: $previousOi, topBidPrice: $topBidPrice, topAskPrice: $topAskPrice, topBidQuantity: $topBidQuantity, topAskQuantity: $topAskQuantity, iv: $iv, delta: $delta, gamma: $gamma, theta: $theta, vega: $vega)';
}


}

/// @nodoc
abstract mixin class _$OptionDataCopyWith<$Res> implements $OptionDataCopyWith<$Res> {
  factory _$OptionDataCopyWith(_OptionData value, $Res Function(_OptionData) _then) = __$OptionDataCopyWithImpl;
@override @useResult
$Res call({
 String timestamp, String expiryDate, double strikePrice, String optionType, double spotPrice, double lastPrice, double previousClosePrice, int volume, int previousVolume, int oi, int previousOi, double topBidPrice, double topAskPrice, int topBidQuantity, int topAskQuantity, double iv, double delta, double gamma, double theta, double vega
});




}
/// @nodoc
class __$OptionDataCopyWithImpl<$Res>
    implements _$OptionDataCopyWith<$Res> {
  __$OptionDataCopyWithImpl(this._self, this._then);

  final _OptionData _self;
  final $Res Function(_OptionData) _then;

/// Create a copy of OptionData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? timestamp = null,Object? expiryDate = null,Object? strikePrice = null,Object? optionType = null,Object? spotPrice = null,Object? lastPrice = null,Object? previousClosePrice = null,Object? volume = null,Object? previousVolume = null,Object? oi = null,Object? previousOi = null,Object? topBidPrice = null,Object? topAskPrice = null,Object? topBidQuantity = null,Object? topAskQuantity = null,Object? iv = null,Object? delta = null,Object? gamma = null,Object? theta = null,Object? vega = null,}) {
  return _then(_OptionData(
timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as String,expiryDate: null == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as String,strikePrice: null == strikePrice ? _self.strikePrice : strikePrice // ignore: cast_nullable_to_non_nullable
as double,optionType: null == optionType ? _self.optionType : optionType // ignore: cast_nullable_to_non_nullable
as String,spotPrice: null == spotPrice ? _self.spotPrice : spotPrice // ignore: cast_nullable_to_non_nullable
as double,lastPrice: null == lastPrice ? _self.lastPrice : lastPrice // ignore: cast_nullable_to_non_nullable
as double,previousClosePrice: null == previousClosePrice ? _self.previousClosePrice : previousClosePrice // ignore: cast_nullable_to_non_nullable
as double,volume: null == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as int,previousVolume: null == previousVolume ? _self.previousVolume : previousVolume // ignore: cast_nullable_to_non_nullable
as int,oi: null == oi ? _self.oi : oi // ignore: cast_nullable_to_non_nullable
as int,previousOi: null == previousOi ? _self.previousOi : previousOi // ignore: cast_nullable_to_non_nullable
as int,topBidPrice: null == topBidPrice ? _self.topBidPrice : topBidPrice // ignore: cast_nullable_to_non_nullable
as double,topAskPrice: null == topAskPrice ? _self.topAskPrice : topAskPrice // ignore: cast_nullable_to_non_nullable
as double,topBidQuantity: null == topBidQuantity ? _self.topBidQuantity : topBidQuantity // ignore: cast_nullable_to_non_nullable
as int,topAskQuantity: null == topAskQuantity ? _self.topAskQuantity : topAskQuantity // ignore: cast_nullable_to_non_nullable
as int,iv: null == iv ? _self.iv : iv // ignore: cast_nullable_to_non_nullable
as double,delta: null == delta ? _self.delta : delta // ignore: cast_nullable_to_non_nullable
as double,gamma: null == gamma ? _self.gamma : gamma // ignore: cast_nullable_to_non_nullable
as double,theta: null == theta ? _self.theta : theta // ignore: cast_nullable_to_non_nullable
as double,vega: null == vega ? _self.vega : vega // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
