// lib/models/option_data.dart

import 'package:freezed_annotation/freezed_annotation.dart';

part 'option_data.freezed.dart';
part 'option_data.g.dart';

@freezed
abstract class OptionData with _$OptionData {
  const factory OptionData({
    required String timestamp,
    required String expiryDate,
    required double strikePrice,
    required String optionType,
    required double spotPrice,
    required double lastPrice,
    required double previousClosePrice,
    required int volume,
    required int previousVolume,
    required int oi,
    required int previousOi,
    required double topBidPrice,
    required double topAskPrice,
    required int topBidQuantity,
    required int topAskQuantity,
    required double iv,
    required double delta,
    required double gamma,
    required double theta,
    required double vega,
  }) = _OptionData;

  factory OptionData.fromJson(Map<String, dynamic> json) => _$OptionDataFromJson(json);

  // Custom fromMap method with null safety
  factory OptionData.fromMap(Map<String, dynamic> map) {
    return OptionData(
      timestamp: map['timestamp'] as String? ?? '',
      expiryDate: map['expiry_date'] as String? ?? '',
      strikePrice: (map['strike_price'] as num?)?.toDouble() ?? 0.0,
      optionType: map['option_type'] as String? ?? '',
      spotPrice: (map['spot_price'] as num?)?.toDouble() ?? 0.0,
      lastPrice: (map['last_price'] as num?)?.toDouble() ?? 0.0,
      previousClosePrice: (map['previous_close_price'] as num?)?.toDouble() ?? 0.0,
      volume: (map['volume'] as num?)?.toInt() ?? 0,
      previousVolume: (map['previous_volume'] as num?)?.toInt() ?? 0,
      oi: (map['oi'] as num?)?.toInt() ?? 0,
      previousOi: (map['previous_oi'] as num?)?.toInt() ?? 0,
      topBidPrice: (map['top_bid_price'] as num?)?.toDouble() ?? 0.0,
      topAskPrice: (map['top_ask_price'] as num?)?.toDouble() ?? 0.0,
      topBidQuantity: (map['top_bid_quantity'] as num?)?.toInt() ?? 0,
      topAskQuantity: (map['top_ask_quantity'] as num?)?.toInt() ?? 0,
      iv: (map['iv'] as num?)?.toDouble() ?? 0.0,
      delta: (map['delta'] as num?)?.toDouble() ?? 0.0,
      gamma: (map['gamma'] as num?)?.toDouble() ?? 0.0,
      theta: (map['theta'] as num?)?.toDouble() ?? 0.0,
      vega: (map['vega'] as num?)?.toDouble() ?? 0.0,
    );
  }
}
