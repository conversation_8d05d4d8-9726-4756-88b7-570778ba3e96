import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/user_profile_simple.dart';

class OnboardingStepWidget extends HookConsumerWidget {
  final OnboardingStep step;
  final bool isFirst;
  final bool isLast;
  final VoidCallback onNext;
  final VoidCallback onPrevious;

  const OnboardingStepWidget({
    super.key,
    required this.step,
    required this.isFirst,
    required this.isLast,
    required this.onNext,
    required this.onPrevious,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(color: theme.colorScheme.primaryContainer, shape: BoxShape.circle),
            child: Icon(_getIconData(step.icon), size: 60, color: theme.colorScheme.primary),
          ),

          const SizedBox(height: 32),

          // Title
          Text(
            step.title,
            style: theme.textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Description
          Text(
            step.description,
            style: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurfaceVariant),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 48),

          // Step-specific content
          Expanded(child: _buildStepContent(context, ref)),

          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (!isFirst) TextButton(onPressed: onPrevious, child: const Text('Previous')) else const SizedBox.shrink(),

              ElevatedButton(
                onPressed: onNext,
                style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12)),
                child: Text(isLast ? 'Get Started' : 'Next'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(BuildContext context, WidgetRef ref) {
    switch (step.id) {
      case 'welcome':
        return _buildWelcomeContent(context);
      case 'profile':
        return _buildProfileContent(context, ref);
      case 'preferences':
        return _buildPreferencesContent(context, ref);
      case 'market_selection':
        return _buildMarketSelectionContent(context, ref);
      case 'ai_setup':
        return _buildAISetupContent(context, ref);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildWelcomeContent(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Icon(Icons.analytics, size: 48, color: theme.colorScheme.primary),
                const SizedBox(height: 16),
                Text('AI-Powered Trading Analytics', style: theme.textTheme.titleLarge, textAlign: TextAlign.center),
                const SizedBox(height: 8),
                Text(
                  'Get intelligent insights, real-time analytics, and personalized trading recommendations.',
                  style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileContent(BuildContext context, WidgetRef ref) {
    final nameController = useTextEditingController();
    final phoneController = useTextEditingController();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(labelText: 'Full Name', prefixIcon: Icon(Icons.person)),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: phoneController,
              decoration: const InputDecoration(labelText: 'Phone Number (Optional)', prefixIcon: Icon(Icons.phone)),
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferencesContent(BuildContext context, WidgetRef ref) {
    final notifications = useState(true);
    final emailUpdates = useState(true);
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            SwitchListTile(
              title: const Text('Push Notifications'),
              subtitle: const Text('Get real-time trading alerts'),
              value: notifications.value,
              onChanged: (value) => notifications.value = value,
            ),
            SwitchListTile(
              title: const Text('Email Updates'),
              subtitle: const Text('Receive market insights via email'),
              value: emailUpdates.value,
              onChanged: (value) => emailUpdates.value = value,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarketSelectionContent(BuildContext context, WidgetRef ref) {
    final selectedMarkets = useState<Set<String>>({'US'});

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Text('Select your preferred markets:', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              children:
                  ['US', 'EU', 'Asia', 'Crypto'].map((market) {
                    return FilterChip(
                      label: Text(market),
                      selected: selectedMarkets.value.contains(market),
                      onSelected: (selected) {
                        final newSelection = Set<String>.from(selectedMarkets.value);
                        if (selected) {
                          newSelection.add(market);
                        } else {
                          newSelection.remove(market);
                        }
                        selectedMarkets.value = newSelection;
                      },
                    );
                  }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAISetupContent(BuildContext context, WidgetRef ref) {
    final riskTolerance = useState<double>(0.5);
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Text('Configure your AI assistant:', style: theme.textTheme.titleMedium),
            const SizedBox(height: 24),
            Text('Risk Tolerance: ${(riskTolerance.value * 100).toInt()}%', style: theme.textTheme.bodyLarge),
            Slider(
              value: riskTolerance.value,
              onChanged: (value) => riskTolerance.value = value,
              min: 0.0,
              max: 1.0,
              divisions: 10,
            ),
            const SizedBox(height: 16),
            Text(
              riskTolerance.value < 0.3
                  ? 'Conservative - Lower risk, stable returns'
                  : riskTolerance.value < 0.7
                  ? 'Balanced - Moderate risk and returns'
                  : 'Aggressive - Higher risk, potential for higher returns',
              style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'welcome':
        return Icons.waving_hand;
      case 'profile':
        return Icons.person;
      case 'settings':
        return Icons.settings;
      case 'market':
        return Icons.trending_up;
      case 'ai':
        return Icons.psychology;
      default:
        return Icons.info;
    }
  }
}
