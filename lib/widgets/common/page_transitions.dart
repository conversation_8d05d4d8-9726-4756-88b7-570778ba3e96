import 'package:flutter/material.dart';

class SlidePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final SlideDirection direction;
  final Duration duration;

  SlidePageRoute({
    required this.child,
    this.direction = SlideDirection.rightToLeft,
    this.duration = const Duration(milliseconds: 300),
  }) : super(
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => child,
        );

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    Offset begin;
    const end = Offset.zero;
    const curve = Curves.easeInOut;

    switch (direction) {
      case SlideDirection.rightToLeft:
        begin = const Offset(1.0, 0.0);
        break;
      case SlideDirection.leftToRight:
        begin = const Offset(-1.0, 0.0);
        break;
      case SlideDirection.topToBottom:
        begin = const Offset(0.0, -1.0);
        break;
      case SlideDirection.bottomToTop:
        begin = const Offset(0.0, 1.0);
        break;
    }

    var tween = Tween(begin: begin, end: end);
    var offsetAnimation = animation.drive(tween.chain(CurveTween(curve: curve)));

    return SlideTransition(
      position: offsetAnimation,
      child: FadeTransition(
        opacity: animation,
        child: child,
      ),
    );
  }
}

class ScalePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final Duration duration;

  ScalePageRoute({
    required this.child,
    this.duration = const Duration(milliseconds: 300),
  }) : super(
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => child,
        );

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    const curve = Curves.easeInOut;
    
    var scaleTween = Tween<double>(begin: 0.8, end: 1.0);
    var opacityTween = Tween<double>(begin: 0.0, end: 1.0);
    
    var scaleAnimation = animation.drive(scaleTween.chain(CurveTween(curve: curve)));
    var opacityAnimation = animation.drive(opacityTween.chain(CurveTween(curve: curve)));

    return ScaleTransition(
      scale: scaleAnimation,
      child: FadeTransition(
        opacity: opacityAnimation,
        child: child,
      ),
    );
  }
}

class FadePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final Duration duration;

  FadePageRoute({
    required this.child,
    this.duration = const Duration(milliseconds: 300),
  }) : super(
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => child,
        );

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }
}

class RotationPageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final Duration duration;

  RotationPageRoute({
    required this.child,
    this.duration = const Duration(milliseconds: 400),
  }) : super(
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => child,
        );

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    const curve = Curves.easeInOut;
    
    var rotationTween = Tween<double>(begin: 0.1, end: 0.0);
    var scaleTween = Tween<double>(begin: 0.8, end: 1.0);
    var opacityTween = Tween<double>(begin: 0.0, end: 1.0);
    
    var rotationAnimation = animation.drive(rotationTween.chain(CurveTween(curve: curve)));
    var scaleAnimation = animation.drive(scaleTween.chain(CurveTween(curve: curve)));
    var opacityAnimation = animation.drive(opacityTween.chain(CurveTween(curve: curve)));

    return Transform.rotate(
      angle: rotationAnimation.value,
      child: ScaleTransition(
        scale: scaleAnimation,
        child: FadeTransition(
          opacity: opacityAnimation,
          child: child,
        ),
      ),
    );
  }
}

class SizePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final Duration duration;

  SizePageRoute({
    required this.child,
    this.duration = const Duration(milliseconds: 400),
  }) : super(
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => child,
        );

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    const curve = Curves.easeInOut;
    
    return Align(
      child: SizeTransition(
        sizeFactor: animation.drive(CurveTween(curve: curve)),
        child: FadeTransition(
          opacity: animation,
          child: child,
        ),
      ),
    );
  }
}

enum SlideDirection {
  rightToLeft,
  leftToRight,
  topToBottom,
  bottomToTop,
}

class PageTransitionWrapper {
  static Route<T> slide<T>({
    required Widget child,
    SlideDirection direction = SlideDirection.rightToLeft,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return SlidePageRoute<T>(
      child: child,
      direction: direction,
      duration: duration,
    );
  }

  static Route<T> scale<T>({
    required Widget child,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return ScalePageRoute<T>(
      child: child,
      duration: duration,
    );
  }

  static Route<T> fade<T>({
    required Widget child,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return FadePageRoute<T>(
      child: child,
      duration: duration,
    );
  }

  static Route<T> rotation<T>({
    required Widget child,
    Duration duration = const Duration(milliseconds: 400),
  }) {
    return RotationPageRoute<T>(
      child: child,
      duration: duration,
    );
  }

  static Route<T> size<T>({
    required Widget child,
    Duration duration = const Duration(milliseconds: 400),
  }) {
    return SizePageRoute<T>(
      child: child,
      duration: duration,
    );
  }
}

class AnimatedPageWrapper extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;

  const AnimatedPageWrapper({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
  });

  @override
  State<AnimatedPageWrapper> createState() => _AnimatedPageWrapperState();
}

class _AnimatedPageWrapperState extends State<AnimatedPageWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));
    
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: widget.child,
      ),
    );
  }
}