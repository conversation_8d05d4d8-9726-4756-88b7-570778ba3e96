import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:animate_do/animate_do.dart';
import 'dart:math' as math;

class AnimatedSparkline extends StatefulWidget {
  final List<double> data;
  final Color? color;
  final double height;
  final double strokeWidth;
  final Duration animationDuration;
  final bool showGradient;
  final bool showDots;
  final String? label;
  final String? value;
  final String? change;
  final bool isPositive;

  const AnimatedSparkline({
    super.key,
    required this.data,
    this.color,
    this.height = 60,
    this.strokeWidth = 2.0,
    this.animationDuration = const Duration(milliseconds: 1500),
    this.showGradient = true,
    this.showDots = false,
    this.label,
    this.value,
    this.change,
    this.isPositive = true,
  });

  @override
  State<AnimatedSparkline> createState() => _AnimatedSparklineState();
}

class _AnimatedSparklineState extends State<AnimatedSparkline>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutCubic,
    ));
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final sparklineColor = widget.color ?? 
        (widget.isPositive ? Colors.green : Colors.red);

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final animatedData = widget.data
            .asMap()
            .entries
            .map((entry) {
              final index = entry.key;
              final value = entry.value;
              final progress = _animation.value * widget.data.length;
              return index < progress ? value : widget.data.first;
            })
            .toList();

        return Container(
          height: widget.height,
          child: widget.label != null || widget.value != null
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (widget.label != null || widget.value != null)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (widget.label != null)
                            Text(
                              widget.label!,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          if (widget.value != null)
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  widget.value!,
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if (widget.change != null) ...[
                                  const SizedBox(width: 4),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: sparklineColor.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      widget.change!,
                                      style: theme.textTheme.bodySmall?.copyWith(
                                        color: sparklineColor,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                        ],
                      ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _buildChart(animatedData, sparklineColor, theme),
                    ),
                  ],
                )
              : _buildChart(animatedData, sparklineColor, theme),
        );
      },
    );
  }

  Widget _buildChart(List<double> data, Color color, ThemeData theme) {
    if (data.isEmpty) return const SizedBox();

    final spots = data
        .asMap()
        .entries
        .map((entry) => FlSpot(entry.key.toDouble(), entry.value))
        .toList();

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: false),
        titlesData: const FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: color,
            barWidth: widget.strokeWidth,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: widget.showDots,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 3,
                  color: color,
                  strokeWidth: 0,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: widget.showGradient,
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  color.withOpacity(0.3),
                  color.withOpacity(0.0),
                ],
              ),
            ),
          ),
        ],
        minX: 0,
        maxX: (data.length - 1).toDouble(),
        minY: data.reduce(math.min),
        maxY: data.reduce(math.max),
      ),
      duration: const Duration(milliseconds: 150),
    );
  }
}

class TradingSparklineCard extends StatelessWidget {
  final String title;
  final String value;
  final String change;
  final List<double> data;
  final bool isPositive;
  final IconData? icon;
  final VoidCallback? onTap;

  const TradingSparklineCard({
    super.key,
    required this.title,
    required this.value,
    required this.change,
    required this.data,
    required this.isPositive,
    this.icon,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = isPositive ? Colors.green : Colors.red;

    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.2),
            ),
            boxShadow: [
              BoxShadow(
                color: theme.shadowColor.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  if (icon != null) ...[
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        icon,
                        size: 16,
                        color: color,
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: Text(
                      title,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                  Icon(
                    isPositive ? Icons.trending_up : Icons.trending_down,
                    color: color,
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Text(
                    value,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      change,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 40,
                child: AnimatedSparkline(
                  data: data,
                  color: color,
                  height: 40,
                  showGradient: true,
                  animationDuration: const Duration(milliseconds: 2000),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PulsingTradingIndicator extends StatefulWidget {
  final String label;
  final String value;
  final Color color;
  final IconData icon;
  final double size;

  const PulsingTradingIndicator({
    super.key,
    required this.label,
    required this.value,
    required this.color,
    required this.icon,
    this.size = 60,
  });

  @override
  State<PulsingTradingIndicator> createState() => _PulsingTradingIndicatorState();
}

class _PulsingTradingIndicatorState extends State<PulsingTradingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();
    
    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.size,
            height: widget.size,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Pulsing outer ring
                Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.color.withOpacity(0.2 * (1 - _pulseAnimation.value)),
                  ),
                ),
                // Inner circle
                Container(
                  width: widget.size * 0.7,
                  height: widget.size * 0.7,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.color.withOpacity(0.9),
                    boxShadow: [
                      BoxShadow(
                        color: widget.color.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    widget.icon,
                    color: Colors.white,
                    size: widget.size * 0.3,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class AnimatedProgressSparkline extends StatefulWidget {
  final double progress;
  final String label;
  final Color color;
  final Duration animationDuration;

  const AnimatedProgressSparkline({
    super.key,
    required this.progress,
    required this.label,
    required this.color,
    this.animationDuration = const Duration(milliseconds: 1500),
  });

  @override
  State<AnimatedProgressSparkline> createState() => _AnimatedProgressSparklineState();
}

class _AnimatedProgressSparklineState extends State<AnimatedProgressSparkline>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Text(
                  '${(_animation.value * 100).toInt()}%',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: widget.color,
                    fontWeight: FontWeight.bold,
                  ),
                );
              },
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 6,
          decoration: BoxDecoration(
            color: widget.color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(3),
          ),
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: _animation.value,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        widget.color,
                        widget.color.withOpacity(0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

// Market data generator for demo purposes
class MarketDataGenerator {
  static List<double> generateSparklineData({
    int length = 20,
    double baseValue = 100,
    double volatility = 0.05,
    bool trending = true,
  }) {
    final random = math.Random();
    final data = <double>[];
    double currentValue = baseValue;

    for (int i = 0; i < length; i++) {
      final change = (random.nextDouble() - 0.5) * 2 * volatility * baseValue;
      final trend = trending ? (i / length) * volatility * baseValue : 0;
      currentValue += change + trend;
      data.add(currentValue);
    }

    return data;
  }

  static String formatChange(double current, double previous) {
    final change = current - previous;
    final percentage = (change / previous) * 100;
    final sign = change >= 0 ? '+' : '';
    return '$sign${percentage.toStringAsFixed(2)}%';
  }

  static String formatValue(double value, {String currency = '\$'}) {
    if (value >= 1000000) {
      return '$currency${(value / 1000000).toStringAsFixed(2)}M';
    } else if (value >= 1000) {
      return '$currency${(value / 1000).toStringAsFixed(2)}K';
    } else {
      return '$currency${value.toStringAsFixed(2)}';
    }
  }
}