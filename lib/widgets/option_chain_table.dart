import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'dart:math'; // Add this import for min function
import '../providers/riverpod/options_provider.dart';
import '../models/option_data.dart';

class OptionDataSource extends DataGridSource {
  final NumberFormat formatter = NumberFormat("#,##0.00");
  final NumberFormat percentFormatter = NumberFormat("#0.0%");
  final List<DataGridRow> _rows = [];
  final double atmStrike;
  final Map<double, Color> strikeColors = {};
  final BuildContext context;
  final WidgetRef ref; // Add ref to access providers

  // Maps to store column rankings
  final Map<String, List<MapEntry<int, dynamic>>> _columnRankings = {};

  // Format numbers with K, M suffix
  String formatCompactNumber(num number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(2)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(0)}K';
    }
    return number.toString();
  }

  // Format percentage values
  String formatPercentage(double value) {
    return '${(value * 100).toStringAsFixed(1)}%';
  }

  OptionDataSource({
    required Map<double, Map<String, OptionData>> organizedData,
    required this.atmStrike,
    required this.context,
    required this.ref,
  }) {
    // Sort strike prices
    final sortedStrikes = organizedData.keys.toList()..sort();

    // Find spot price from first option or use ATM strike if not available
    final spotPrice =
        organizedData.isNotEmpty &&
                organizedData.values.first.isNotEmpty &&
                (organizedData.values.first['ce'] != null || organizedData.values.first['pe'] != null)
            ? (organizedData.values.first['ce']?.spotPrice ?? organizedData.values.first['pe']?.spotPrice ?? atmStrike)
            : atmStrike;

    // Get highest volumes for each option type for relative volume calculation
    final optionsNotifier = ref.read(optionsProvider.notifier);
    final ceHighestVolume = optionsNotifier.getHighestVolume('ce');
    final peHighestVolume = optionsNotifier.getHighestVolume('pe');

    // Find appropriate position to insert current price row
    int currentPriceInsertIndex = -1;

    // Handle case where spot price is below lowest strike
    if (sortedStrikes.isNotEmpty && spotPrice < sortedStrikes.first) {
      currentPriceInsertIndex = 0;
    }
    // Handle case where spot price is above highest strike
    else if (sortedStrikes.isNotEmpty && spotPrice > sortedStrikes.last) {
      currentPriceInsertIndex = sortedStrikes.length;
    }
    // Handle case where spot price is between strikes
    else {
      for (int i = 0; i < sortedStrikes.length - 1; i++) {
        if (sortedStrikes[i] < spotPrice && sortedStrikes[i + 1] > spotPrice) {
          currentPriceInsertIndex = i + 1;
          break;
        }
      }
    }

    // Initial data construction with rows
    for (int i = 0; i < sortedStrikes.length; i++) {
      final strike = sortedStrikes[i];

      // Insert current price row if this is the position
      if (i == currentPriceInsertIndex) {
        _rows.add(_createCurrentPriceRow(spotPrice));
      }

      final OptionData? callOption = organizedData[strike]?['ce'];
      final OptionData? putOption = organizedData[strike]?['pe'];

      // Calculate changes and percentages
      final callOIChange = callOption != null ? (callOption.oi - callOption.previousOi) : 0;
      final putOIChange = putOption != null ? (putOption.oi - putOption.previousOi) : 0;

      // Calculate OI change percentage for CE
      final callOIChangePercent =
          (callOIChange != 0 || putOIChange != 0) ? (callOIChange.abs() / (callOIChange.abs() + putOIChange.abs())) : 0.0;

      // Calculate OI change percentage for PE
      final putOIChangePercent =
          (callOIChange != 0 || putOIChange != 0) ? (putOIChange.abs() / (callOIChange.abs() + putOIChange.abs())) : 0.0;

      // Calculate percentage change in OI (for display in the OI Chg column)
      final callOIPercentChange =
          callOption != null && callOption.previousOi != 0
              ? (callOption.oi - callOption.previousOi) / callOption.previousOi
              : 0.0;
      final putOIPercentChange =
          putOption != null && putOption.previousOi != 0 ? (putOption.oi - putOption.previousOi) / putOption.previousOi : 0.0;

      // Calculate volume change from market open (first timestamp data)
      final callVolumeChangePercent = callOption != null ? optionsNotifier.getVolumeChangePercent(strike, 'ce') : 0.0;

      final putVolumeChangePercent = putOption != null ? optionsNotifier.getVolumeChangePercent(strike, 'pe') : 0.0;

      // Calculate relative volume percentage (compared to highest volume for that option type)
      final callRelativeVolumePercent = callOption != null && ceHighestVolume > 0 ? callOption.volume / ceHighestVolume : 0.0;

      final putRelativeVolumePercent = putOption != null && peHighestVolume > 0 ? putOption.volume / peHighestVolume : 0.0;

      final callPriceChange =
          callOption != null
              ? (callOption.lastPrice - callOption.previousClosePrice) /
                  (callOption.previousClosePrice == 0 ? 1 : callOption.previousClosePrice)
              : 0.0;
      final putPriceChange =
          putOption != null
              ? (putOption.lastPrice - putOption.previousClosePrice) /
                  (putOption.previousClosePrice == 0 ? 1 : putOption.previousClosePrice)
              : 0.0;

      // Special marking for ATM row
      // if (strike == atmStrike) {
      //   strikeColors[strike] = Theme.of(context).primaryColor.withOpacity(0.15);
      // }

      _rows.add(
        DataGridRow(
          cells: [
            // CALL side
            DataGridCell<Map<String, dynamic>>(
              columnName: 'ceIV',
              value: {
                'displayValue':
                    callOption != null ? '${formatter.format(callOption.iv * 100)}\n${formatter.format(callOption.delta)}' : '-',
                'sortValue': callOption?.iv ?? 0.0,
              },
            ),
            DataGridCell<Map<String, dynamic>>(
              columnName: 'ceOIChg',
              value: {
                'displayValue': '${callOIPercentChange >= 0 ? '+' : ''}${(callOIPercentChange * 100).toStringAsFixed(1)}%',
                'sortValue': callOIPercentChange,
              },
            ),
            DataGridCell<Map<String, dynamic>>(
              columnName: 'ceOI',
              value: {
                'displayValue':
                    callOption != null
                        ? '${(callOIChangePercent * 100).toStringAsFixed(1)}%\n${formatCompactNumber(callOption.oi)}'
                        : '-',
                'sortValue': callOption?.oi.toDouble() ?? 0.0,
              },
            ),
            DataGridCell<Map<String, dynamic>>(
              columnName: 'ceVolume',
              value: {
                'displayValue': callOption != null ? '${formatCompactNumber(callOption.volume)}' : '-',
                'sortValue': callOption?.volume.toDouble() ?? 0.0,
                'volumeChangePercent': callVolumeChangePercent,
              },
            ),
            DataGridCell<Map<String, dynamic>>(
              columnName: 'ceLTP',
              value: {
                'displayValue':
                    callOption != null
                        ? '${formatter.format(callOption.lastPrice)}\n${callPriceChange >= 0 ? '+' : ''}${formatter.format(callPriceChange * 100)}%'
                        : '-',
                'sortValue': callOption?.lastPrice ?? 0.0,
              },
            ),

            // MIDDLE (Strike price)
            DataGridCell<Map<String, dynamic>>(
              columnName: 'strike',
              value: {
                'displayValue':
                    '${strike.toInt()}\n${putOption != null && callOption != null ? formatter.format(putOption.oi / callOption.oi) : '-'}',
                'sortValue': strike,
                'isAtm': false,
              },
            ),

            // PUT side
            DataGridCell<Map<String, dynamic>>(
              columnName: 'peLTP',
              value: {
                'displayValue':
                    putOption != null
                        ? '${formatter.format(putOption.lastPrice)}\n${putPriceChange >= 0 ? '+' : ''}${formatter.format(putPriceChange * 100)}%'
                        : '-',
                'sortValue': putOption?.lastPrice ?? 0.0,
              },
            ),
            DataGridCell<Map<String, dynamic>>(
              columnName: 'peVolume',
              value: {
                'displayValue': putOption != null ? '${formatCompactNumber(putOption.volume)}' : '-',
                'sortValue': putOption?.volume.toDouble() ?? 0.0,
                'volumeChangePercent': putVolumeChangePercent,
              },
            ),
            DataGridCell<Map<String, dynamic>>(
              columnName: 'peOI',
              value: {
                'displayValue':
                    putOption != null
                        ? '${(putOIChangePercent * 100).toStringAsFixed(1)}%\n${formatCompactNumber(putOption.oi)}'
                        : '-',
                'sortValue': putOption?.oi.toDouble() ?? 0.0,
              },
            ),
            DataGridCell<Map<String, dynamic>>(
              columnName: 'peOIChg',
              value: {
                'displayValue': '${putOIPercentChange >= 0 ? '+' : ''}${(putOIPercentChange * 100).toStringAsFixed(1)}%',
                'sortValue': putOIPercentChange,
              },
            ),
            DataGridCell<Map<String, dynamic>>(
              columnName: 'peIV',
              value: {
                'displayValue':
                    putOption != null ? '${formatter.format(putOption.iv * 100)}\n${formatter.format(putOption.delta)}' : '-',
                'sortValue': putOption?.iv ?? 0.0,
              },
            ),
          ],
        ),
      );
    }

    // Calculate rankings for each column
    _calculateColumnRankings();
  }

  // Calculate the top values for each column
  void _calculateColumnRankings() {
    // List of column names to rank (excluding strike)
    final columnsToRank = ['ceIV', 'ceOIChg', 'ceOI', 'ceVolume', 'ceLTP', 'peLTP', 'peVolume', 'peOI', 'peOIChg', 'peIV'];

    // For each column, collect values and sort
    for (final columnName in columnsToRank) {
      final values = <MapEntry<int, dynamic>>[];

      for (int i = 0; i < _rows.length; i++) {
        try {
          final cell = _rows[i].getCells().firstWhere((cell) => cell.columnName == columnName);
          final sortValue = (cell.value as Map<String, dynamic>)['sortValue'];

          // Only include non-zero values
          if (sortValue != null && sortValue != 0.0) {
            values.add(MapEntry(i, sortValue));
          }
        } catch (e) {
          // Skip this row if there's any error retrieving data
          continue;
        }
      }

      // Sort values based on column type
      if (values.isEmpty) {
        _columnRankings[columnName] = [];
        continue;
      }

      if (columnName.contains('OIChg')) {
        // For OI change, we want to rank by absolute value
        values.sort((a, b) => (b.value as double).abs().compareTo((a.value as double).abs()));
      } else {
        // For others, sort by descending value
        values.sort((a, b) => (b.value as double).compareTo(a.value as double));
      }

      _columnRankings[columnName] = values.take(min(3, values.length)).toList();
    }
  }

  // Get the rank of a cell value in its column
  int getRank(int rowIndex, String columnName) {
    final rankings = _columnRankings[columnName];
    if (rankings == null) return -1;

    for (int i = 0; i < rankings.length; i++) {
      if (rankings[i].key == rowIndex) {
        return i;
      }
    }
    return -1;
  }

  // Get the current price row index for scrolling
  int getCurrentPriceRowIndex() {
    for (int i = 0; i < _rows.length; i++) {
      final cell = _rows[i].getCells().firstWhere((cell) => cell.columnName == 'strike');
      final isCurrentPrice = (cell.value as Map<String, dynamic>)['isCurrentPrice'] == true;
      if (isCurrentPrice) return i;
    }
    return getAtmRowIndex(); // Fall back to ATM row if current price row not found
  }

  // Get the ATM row index for scrolling (legacy method, kept for fallback)
  int getAtmRowIndex() {
    for (int i = 0; i < _rows.length; i++) {
      final strikeCell = _rows[i].getCells().firstWhere((cell) => cell.columnName == 'strike');
      final isAtm = (strikeCell.value as Map<String, dynamic>)['isAtm'] == true;
      if (isAtm) return i;
    }
    return 0;
  }

  // Create a special row to display current price
  DataGridRow _createCurrentPriceRow(double spotPrice) {
    return DataGridRow(
      cells: [
        // Create empty cells for all columns with special formatting for strike column
        DataGridCell<Map<String, dynamic>>(columnName: 'ceIV', value: {'displayValue': '', 'sortValue': 0.0}),
        DataGridCell<Map<String, dynamic>>(columnName: 'ceOIChg', value: {'displayValue': '', 'sortValue': 0.0}),
        DataGridCell<Map<String, dynamic>>(columnName: 'ceOI', value: {'displayValue': '', 'sortValue': 0.0}),
        DataGridCell<Map<String, dynamic>>(columnName: 'ceVolume', value: {'displayValue': '', 'sortValue': 0.0}),
        DataGridCell<Map<String, dynamic>>(columnName: 'ceLTP', value: {'displayValue': '', 'sortValue': 0.0}),

        // Middle column (strike) - show current price here
        DataGridCell<Map<String, dynamic>>(
          columnName: 'strike',
          value: {'displayValue': formatter.format(spotPrice), 'sortValue': spotPrice, 'isCurrentPrice': true, 'isAtm': false},
        ),

        DataGridCell<Map<String, dynamic>>(columnName: 'peLTP', value: {'displayValue': '', 'sortValue': 0.0}),
        DataGridCell<Map<String, dynamic>>(columnName: 'peVolume', value: {'displayValue': '', 'sortValue': 0.0}),
        DataGridCell<Map<String, dynamic>>(columnName: 'peOI', value: {'displayValue': '', 'sortValue': 0.0}),
        DataGridCell<Map<String, dynamic>>(columnName: 'peOIChg', value: {'displayValue': '', 'sortValue': 0.0}),
        DataGridCell<Map<String, dynamic>>(columnName: 'peIV', value: {'displayValue': '', 'sortValue': 0.0}),
      ],
    );
  }

  @override
  List<DataGridRow> get rows => _rows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    final rowIndex = _rows.indexOf(row);

    return DataGridRowAdapter(
      cells:
          row.getCells().map<Widget>((cell) {
            final column = cell.columnName;
            final data = cell.value as Map<String, dynamic>;
            final displayValue = data['displayValue'];
            final rank = getRank(rowIndex, column);

            // Special styling for strike column (middle column)
            if (column == 'strike') {
              final isAtm = data['isAtm'] as bool? ?? false;
              final isCurrentPrice = data['isCurrentPrice'] as bool? ?? false;

              // Special styling for current price row
              if (isCurrentPrice) {
                return Container(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(vertical: 2.0),
                  height: 20, // Make row height smaller
                  child: Text(
                    displayValue.toString(),
                    style: TextStyle(fontSize: 10, color: Theme.of(context).colorScheme.primary, fontStyle: FontStyle.italic),
                    textAlign: TextAlign.center,
                  ),
                );
              }

              // Normal strike price row - no special ATM styling
              return Container(
                color: Colors.transparent, // No special color for any strike
                alignment: Alignment.center,
                padding: const EdgeInsets.all(6.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children:
                      (displayValue?.toString() ?? "").split('\n').where((line) => line.isNotEmpty).map<Widget>((line) {
                        return Text(line, style: TextStyle(fontSize: 12), textAlign: TextAlign.center);
                      }).toList(),
                ),
              );
            }

            // Styling for volume columns - show volume change percent
            if (column == 'ceVolume' || column == 'peVolume') {
              final volumeChangePercent = data['volumeChangePercent'] as double? ?? 0.0;
              final parts = displayValue.toString().split('\n');
              final volumeValueText = parts.isNotEmpty ? parts[0] : '-';

              // Determine color based on volume change percent (from market open)
              Color volumeChangeColor;
              if (volumeChangePercent > 0) {
                volumeChangeColor = Colors.green[400]!;
              } else if (volumeChangePercent < 0) {
                volumeChangeColor = Colors.red[400]!;
              } else {
                volumeChangeColor = Colors.grey;
              }

              return Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.all(6.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Volume value
                    Text(
                      volumeValueText,
                      style: TextStyle(fontSize: 12),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                    // Volume change percent from market open
                    Text(
                      '${volumeChangePercent > 0 ? '+' : ''}${(volumeChangePercent * 100).toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: volumeChangeColor,
                        fontSize: 11,
                        fontWeight: volumeChangePercent.abs() > 0.1 ? FontWeight.bold : FontWeight.normal,
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }

            // General styling for other columns
            Color? textColor;

            // Styling for IV columns
            if (column == 'ceIV' || column == 'peIV') {
              if (rank == 0)
                textColor = Colors.green[400];
              else if (rank == 1)
                textColor = Colors.green[300];
              else if (rank == 2)
                textColor = Colors.green[200];
            }
            // Styling for OI Change columns
            else if (column == 'ceOIChg' || column == 'peOIChg') {
              final sortValue = data['sortValue'] as double;
              if (sortValue > 0) {
                textColor = Colors.green[400];
              } else if (sortValue < 0) {
                textColor = Colors.red[400];
              }
            }
            // Styling for Price columns
            else if (column == 'ceLTP' || column == 'peLTP') {
              if (rank >= 0 && rank <= 2) {
                textColor = Colors.green[300 + (rank * 100)];
              }
            }
            // Styling for OI columns
            else if (column == 'ceOI' || column == 'peOI') {
              if (rank == 0)
                textColor = Colors.blue[400];
              else if (rank == 1)
                textColor = Colors.blue[300];
              else if (rank == 2)
                textColor = Colors.blue[200];
            }

            return Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(6.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children:
                    (displayValue?.toString() ?? "").split('\n').where((line) => line.isNotEmpty).map<Widget>((line) {
                      return Text(
                        line,
                        style: TextStyle(color: textColor, fontSize: 12),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      );
                    }).toList(),
              ),
            );
          }).toList(),
    );
  }
}

class OptionChainTable extends ConsumerStatefulWidget {
  const OptionChainTable({super.key});

  @override
  ConsumerState<OptionChainTable> createState() => _OptionChainTableState();
}

class _OptionChainTableState extends ConsumerState<OptionChainTable> {
  final DataGridController _dataGridController = DataGridController();
  bool _isInitialScrollComplete = false;

  @override
  void dispose() {
    _dataGridController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(OptionChainTable oldWidget) {
    super.didUpdateWidget(oldWidget);

    // This helps ensure we scroll to the current price row on data refresh
    final optionsState = ref.read(optionsProvider);
    if (optionsState.lastTimestamp != null && optionsState.optionChain.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Don't interrupt the initial scrolling setup
        if (_isInitialScrollComplete) {
          // No direct way to access dataSource from controller, so retrieve from current state
          final atmStrike = ref.read(optionsProvider.notifier).getAtmStrike();
          final organizedData = _prepareOrganizedData(optionsState.optionChain);
          final dataSource = OptionDataSource(organizedData: organizedData, atmStrike: atmStrike, context: context, ref: ref);

          final currentPriceRowIndex = dataSource.getCurrentPriceRowIndex();
          if (currentPriceRowIndex > 0) {
            _dataGridController.scrollToRow(currentPriceRowIndex.toDouble(), position: DataGridScrollPosition.center);
          }
        }
      });
    }
  }

  // Helper method to create organized data map (avoid code duplication)
  Map<double, Map<String, OptionData>> _prepareOrganizedData(List<OptionData> optionChain) {
    final Map<double, Map<String, OptionData>> organizedData = {};

    for (final option in optionChain) {
      if (!organizedData.containsKey(option.strikePrice)) {
        organizedData[option.strikePrice] = {};
      }

      if (option.optionType.toLowerCase() == 'ce') {
        organizedData[option.strikePrice]!['ce'] = option;
      } else if (option.optionType.toLowerCase() == 'pe') {
        organizedData[option.strikePrice]!['pe'] = option;
      }
    }

    return organizedData;
  }

  // Scroll to current price row
  void _scrollToCurrentPriceRow(OptionDataSource dataSource) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isInitialScrollComplete) {
        final currentPriceRowIndex = dataSource.getCurrentPriceRowIndex();
        if (currentPriceRowIndex > 0) {
          // Calculate middle position to center the current price row
          _dataGridController.scrollToRow(currentPriceRowIndex.toDouble(), position: DataGridScrollPosition.center);
          setState(() {
            _isInitialScrollComplete = true;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).primaryColor;

    // Get options state from riverpod
    final optionsState = ref.watch(optionsProvider);
    final optionsNotifier = ref.read(optionsProvider.notifier);

    // Only show error message if needed, but don't show loading spinner
    if (optionsState.error != null) {
      return Center(child: Text(optionsState.error!));
    }

    // If data isn't available yet, show empty table skeleton instead of loading indicator
    if (optionsState.optionChain.isEmpty) {
      return const Center(child: Text('Loading data...', style: TextStyle(color: Colors.grey)));
    }

    final atmStrike = optionsNotifier.getAtmStrike();
    final spotPrice = optionsState.optionChain.isNotEmpty ? optionsState.optionChain.first.spotPrice : 0.0;

    // Create a map of strike prices to organize call and put data
    final Map<double, Map<String, OptionData>> organizedData = {};

    // Process data to organize by strike price and option type
    for (final option in optionsState.optionChain) {
      if (!organizedData.containsKey(option.strikePrice)) {
        organizedData[option.strikePrice] = {};
      }

      if (option.optionType.toLowerCase() == 'ce') {
        organizedData[option.strikePrice]!['ce'] = option;
      } else if (option.optionType.toLowerCase() == 'pe') {
        organizedData[option.strikePrice]!['pe'] = option;
      }
    }

    // Create data source
    final dataSource = OptionDataSource(organizedData: organizedData, atmStrike: atmStrike, context: context, ref: ref);

    // Auto-scroll to current price row
    _scrollToCurrentPriceRow(dataSource);

    // Calculate responsive column widths based on total screen width
    final screenWidth = MediaQuery.of(context).size.width;

    // Calculate proportional column widths to fill the entire screen
    // Total units: 100 (percentages)
    final double totalUnits = 100.0;

    // Assign width units to different column types
    final double ivUnit = 8.0; // 8% for IV/delta columns (x2)
    final double oiChgUnit = 8.0; // 8% for OI change columns (x2)
    final double oiUnit = 9.0; // 9% for OI columns (x2)
    final double volumeUnit = 9.0; // 9% for volume columns (x2)
    final double ltpUnit = 10.0; // 10% for LTP columns (x2)
    final double strikeUnit = 12.0; // 12% for strike column

    // Calculate actual widths
    double ivWidth = screenWidth * ivUnit / totalUnits;
    double oiChgWidth = screenWidth * oiChgUnit / totalUnits;
    double oiWidth = screenWidth * oiUnit / totalUnits;
    double volumeWidth = screenWidth * volumeUnit / totalUnits;
    double ltpWidth = screenWidth * ltpUnit / totalUnits;
    double strikeWidth = screenWidth * strikeUnit / totalUnits;

    return Column(
      children: [
        // Top bar with refresh control and timestamp
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // Auto-refresh toggle
              Row(
                children: [
                  Switch(
                    value: optionsState.autoRefreshEnabled,
                    onChanged: (value) {
                      optionsNotifier.setAutoRefresh(value);
                    },
                  ),
                  const SizedBox(width: 4),
                  Text('Auto Update', style: Theme.of(context).textTheme.bodySmall),
                ],
              ),

              // Manual refresh button
              IconButton(
                icon: const Icon(Icons.refresh),
                // Always enable refresh button, don't disable during loading
                onPressed: () => optionsNotifier.refresh(),
                tooltip: 'Refresh Data',
              ),
              if (optionsState.lastTimestamp != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text('Last Update: ${optionsState.lastTimestamp?.toLocal()}', style: Theme.of(context).textTheme.bodySmall),
                    ],
                  ),
                ),
            ],
          ),
        ),

        // Expiry date buttons row - show this even when loading
        if (optionsState.expiryDates.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: SizedBox(
              height: 36,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: optionsState.expiryDates.length,
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                itemBuilder: (context, index) {
                  final date = optionsState.expiryDates[index];
                  final expiry = DateTime.parse(date);
                  final isSelected = date == optionsState.selectedExpiryDate;
                  final formattedDate = DateFormat('dd MMM').format(expiry);

                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: Material(
                      color: isSelected ? primaryColor : Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(16),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            _isInitialScrollComplete = false;
                          });
                          optionsNotifier.setSelectedExpiryDate(date);
                        },
                        borderRadius: BorderRadius.circular(16),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          alignment: Alignment.center,
                          child: Text(
                            formattedDate,
                            style: TextStyle(
                              color:
                                  isSelected
                                      ? Theme.of(context).colorScheme.onPrimary
                                      : Theme.of(context).textTheme.bodyMedium?.color,
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                              fontSize: 13,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

        // Option chain data grid
        Expanded(
          child: SizedBox(
            width: double.infinity,
            child: SfDataGrid(
              source: dataSource,
              controller: _dataGridController,
              columnWidthMode: ColumnWidthMode.none, // Don't use automatic sizing
              gridLinesVisibility: GridLinesVisibility.both,
              headerGridLinesVisibility: GridLinesVisibility.both,
              navigationMode: GridNavigationMode.row,
              allowSorting: true,
              allowColumnsResizing: true,
              columns: [
                // CALL side
                GridColumn(
                  columnName: 'ceIV',
                  width: ivWidth,
                  label: Container(
                    padding: const EdgeInsets.all(8.0),
                    alignment: Alignment.center,
                    child: Text('IV/delta', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                  ),
                ),
                GridColumn(
                  columnName: 'ceOIChg',
                  width: oiChgWidth,
                  label: Container(
                    padding: const EdgeInsets.all(8.0),
                    alignment: Alignment.center,
                    child: Text('OI Chg', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                  ),
                ),
                GridColumn(
                  columnName: 'ceOI',
                  width: oiWidth,
                  label: Container(
                    padding: const EdgeInsets.all(8.0),
                    alignment: Alignment.center,
                    child: Text('OI', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                  ),
                ),
                GridColumn(
                  columnName: 'ceVolume',
                  width: volumeWidth,
                  label: Tooltip(
                    message: 'Current volume and change percentage from market open',
                    child: Container(
                      padding: const EdgeInsets.all(8.0),
                      alignment: Alignment.center,
                      child: Text(
                        'Volume\n(Δ %)',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                GridColumn(
                  columnName: 'ceLTP',
                  width: ltpWidth,
                  label: Container(
                    padding: const EdgeInsets.all(8.0),
                    alignment: Alignment.center,
                    child: Text('LTP', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                  ),
                ),

                // Strike column (middle)
                GridColumn(
                  columnName: 'strike',
                  width: strikeWidth,
                  label: Container(
                    padding: const EdgeInsets.all(8.0),
                    alignment: Alignment.center,
                    child: Text('Strike', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                  ),
                ),

                // PUT side
                GridColumn(
                  columnName: 'peLTP',
                  width: ltpWidth,
                  label: Container(
                    padding: const EdgeInsets.all(8.0),
                    alignment: Alignment.center,
                    child: Text('LTP', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                  ),
                ),
                GridColumn(
                  columnName: 'peVolume',
                  width: volumeWidth,
                  label: Tooltip(
                    message: 'Current volume and change percentage from market open',
                    child: Container(
                      padding: const EdgeInsets.all(8.0),
                      alignment: Alignment.center,
                      child: Text(
                        'Volume\n(Δ %)',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                GridColumn(
                  columnName: 'peOI',
                  width: oiWidth,
                  label: Container(
                    padding: const EdgeInsets.all(8.0),
                    alignment: Alignment.center,
                    child: Text('OI', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                  ),
                ),
                GridColumn(
                  columnName: 'peOIChg',
                  width: oiChgWidth,
                  label: Container(
                    padding: const EdgeInsets.all(8.0),
                    alignment: Alignment.center,
                    child: Text('OI Chg', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                  ),
                ),
                GridColumn(
                  columnName: 'peIV',
                  width: ivWidth,
                  label: Container(
                    padding: const EdgeInsets.all(8.0),
                    alignment: Alignment.center,
                    child: Text('IV/delta', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
