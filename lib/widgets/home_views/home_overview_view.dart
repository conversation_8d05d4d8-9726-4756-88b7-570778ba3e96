import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';
import '../../models/user_profile_simple.dart';
import '../../providers/riverpod/user_provider.dart';
import '../common/common_widgets.dart';

class HomeOverviewView extends ConsumerWidget {
  const HomeOverviewView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfile = ref.watch(userProfileNotifierProvider);
    final theme = Theme.of(context);

    return userProfile.when(
      data: (profile) => _buildHomeContent(context, profile, theme),
      loading: () => const Center(child: WaveLoading()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FadeIn(child: Text('Error loading profile: $error')),
            const SizedBox(height: 16),
            AnimatedButton(
              text: 'Retry',
              onPressed: () => ref.refresh(userProfileNotifierProvider),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHomeContent(BuildContext context, UserProfile? profile, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          FadeInUp(
            duration: const Duration(milliseconds: 500),
            child: GlassCard(
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome back${profile?.fullName != null ? ', ${profile!.fullName}' : ''}!',
                          style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Ready to make some smart trades today?',
                          style: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: Icon(
                      Icons.trending_up,
                      color: theme.colorScheme.primary,
                      size: 32,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Quick Actions
          FadeInUp(
            duration: const Duration(milliseconds: 600),
            child: Text('Quick Actions', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
          ),
          const SizedBox(height: 12),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
            children: [
              _buildQuickActionCard(context, 'Options Chain', Icons.table_chart, () => context.go('/option-chain'), theme, 0),
              _buildQuickActionCard(context, 'AI Analysis', Icons.psychology, () => context.go('/ai-agent'), theme, 100),
              _buildQuickActionCard(context, 'IV Analysis', Icons.analytics, () => context.go('/iv-analysis'), theme, 200),
              _buildQuickActionCard(context, 'OI Analysis', Icons.bar_chart, () => context.go('/oi-analysis'), theme, 300),
            ],
          ),

          const SizedBox(height: 20),

          // Market Summary with Sparklines
          FadeInUp(
            duration: const Duration(milliseconds: 700),
            child: Text('Market Summary', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
          ),
          const SizedBox(height: 12),
          
          // Trading Sparkline Cards
          Row(
            children: [
              Expanded(
                child: TradingSparklineCard(
                  title: 'S&P 500',
                  value: '4,150.25',
                  change: '+1.2%',
                  data: MarketDataGenerator.generateSparklineData(
                    length: 15,
                    baseValue: 4100,
                    volatility: 0.02,
                    trending: true,
                  ),
                  isPositive: true,
                  icon: Icons.trending_up,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TradingSparklineCard(
                  title: 'NASDAQ',
                  value: '12,750.50',
                  change: '+0.8%',
                  data: MarketDataGenerator.generateSparklineData(
                    length: 15,
                    baseValue: 12700,
                    volatility: 0.025,
                    trending: true,
                  ),
                  isPositive: true,
                  icon: Icons.show_chart,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          TradingSparklineCard(
            title: 'VIX (Volatility Index)',
            value: '18.25',
            change: '-2.1%',
            data: MarketDataGenerator.generateSparklineData(
              length: 15,
              baseValue: 19,
              volatility: 0.08,
              trending: false,
            ),
            isPositive: false,
            icon: Icons.waves,
          ),
          
          const SizedBox(height: 20),
          
          // Options Trading Indicators
          FadeInUp(
            duration: const Duration(milliseconds: 800),
            child: Text('Options Activity', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
          ),
          const SizedBox(height: 12),
          
          // Pulsing trading indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Column(
                children: [
                  PulsingTradingIndicator(
                    label: 'Call Volume',
                    value: '2.4M',
                    color: Colors.green,
                    icon: Icons.call_made,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Call Volume',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    '2.4M',
                    style: theme.textTheme.labelLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Column(
                children: [
                  PulsingTradingIndicator(
                    label: 'Put Volume',
                    value: '1.8M',
                    color: Colors.red,
                    icon: Icons.call_received,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Put Volume',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    '1.8M',
                    style: theme.textTheme.labelLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Column(
                children: [
                  PulsingTradingIndicator(
                    label: 'Put/Call Ratio',
                    value: '0.75',
                    color: Colors.orange,
                    icon: Icons.balance,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Put/Call Ratio',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    '0.75',
                    style: theme.textTheme.labelLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Animated Progress Indicators for Market Health
          FadeInUp(
            duration: const Duration(milliseconds: 900),
            child: Text('Market Health', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
          ),
          const SizedBox(height: 12),
          
          AnimatedCard(
            delay: 800,
            child: Column(
              children: [
                AnimatedProgressSparkline(
                  progress: 0.75,
                  label: 'Bullish Sentiment',
                  color: Colors.green,
                ),
                const SizedBox(height: 16),
                AnimatedProgressSparkline(
                  progress: 0.45,
                  label: 'Market Volatility',
                  color: Colors.orange,
                ),
                const SizedBox(height: 16),
                AnimatedProgressSparkline(
                  progress: 0.85,
                  label: 'Options Liquidity',
                  color: Colors.blue,
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Recent Activity
          FadeInUp(
            duration: const Duration(milliseconds: 800),
            child: Text('Recent Activity', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
          ),
          const SizedBox(height: 12),
          AnimatedCard(
            delay: 700,
            child: Column(
              children: [
                _buildActivityItem('AI Recommendation', 'AAPL Call Option suggested', '2 hours ago', Icons.psychology, theme),
                const Divider(),
                _buildActivityItem(
                  'Portfolio Update',
                  'TSLA position closed',
                  '1 day ago',
                  Icons.account_balance_wallet,
                  theme,
                ),
                const Divider(),
                _buildActivityItem('Market Alert', 'High volatility detected', '2 days ago', Icons.warning, theme),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(BuildContext context, String title, IconData icon, VoidCallback onTap, ThemeData theme, int delay) {
    return FadeInUp(
      duration: const Duration(milliseconds: 300),
      delay: Duration(milliseconds: 600 + delay),
      child: AnimatedCard(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, size: 32, color: theme.colorScheme.primary),
            ),
            const SizedBox(height: 12),
            Text(
              title, 
              style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600), 
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }


  Widget _buildActivityItem(String title, String subtitle, String time, IconData icon, ThemeData theme) {
    return Row(
      children: [
        CircleAvatar(
          backgroundColor: theme.colorScheme.primaryContainer,
          child: Icon(icon, color: theme.colorScheme.primary, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600)),
              Text(subtitle, style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
            ],
          ),
        ),
        Text(time, style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
      ],
    );
  }
}
