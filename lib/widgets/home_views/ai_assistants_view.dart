import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AIAssistantsView extends ConsumerWidget {
  const AIAssistantsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('AI Assistants', style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Text(
            'Your intelligent trading companions',
            style: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurfaceVariant),
          ),
          const SizedBox(height: 20),

          // Active AI Recommendations
          _buildSection('Active Recommendations', _buildRecommendations(theme), theme),

          const SizedBox(height: 20),

          // AI Assistant Cards
          _buildSection('Available Assistants', _buildAssistantCards(theme), theme),

          const SizedBox(height: 20),

          // Performance Summary
          _buildSection('AI Performance', _buildPerformanceSummary(theme), theme),
        ],
      ),
    );
  }

  Widget _buildSection(String title, Widget content, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  Widget _buildRecommendations(ThemeData theme) {
    final recommendations = [
      {
        'type': 'Call',
        'symbol': 'AAPL',
        'strike': '175',
        'expiry': 'Mar 15',
        'confidence': '85%',
        'reason': 'Strong technical breakout expected',
      },
      {
        'type': 'Put',
        'symbol': 'SPY',
        'strike': '410',
        'expiry': 'Mar 08',
        'confidence': '72%',
        'reason': 'Market correction anticipated',
      },
      {
        'type': 'Call',
        'symbol': 'NVDA',
        'strike': '500',
        'expiry': 'Mar 22',
        'confidence': '78%',
        'reason': 'AI sector momentum continues',
      },
    ];

    return Column(
      children:
          recommendations
              .map(
                (rec) => _buildRecommendationCard(
                  rec['type']!,
                  rec['symbol']!,
                  rec['strike']!,
                  rec['expiry']!,
                  rec['confidence']!,
                  rec['reason']!,
                  theme,
                ),
              )
              .toList(),
    );
  }

  Widget _buildAssistantCards(ThemeData theme) {
    final assistants = [
      {
        'name': 'Options Scout',
        'description': 'Identifies high-probability options trades based on technical analysis',
        'status': 'Active',
        'icon': Icons.search,
      },
      {
        'name': 'Risk Manager',
        'description': 'Monitors portfolio risk and suggests position adjustments',
        'status': 'Active',
        'icon': Icons.shield,
      },
      {
        'name': 'Volatility Hunter',
        'description': 'Finds opportunities in high-volatility events and earnings',
        'status': 'Paused',
        'icon': Icons.trending_up,
      },
      {
        'name': 'Swing Trader',
        'description': 'Identifies medium-term swing trading opportunities',
        'status': 'Active',
        'icon': Icons.show_chart,
      },
    ];

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 0.8,
      children:
          assistants
              .map(
                (assistant) => _buildAssistantCard(
                  assistant['name'] as String,
                  assistant['description'] as String,
                  assistant['status'] as String,
                  assistant['icon'] as IconData,
                  theme,
                ),
              )
              .toList(),
    );
  }

  Widget _buildPerformanceSummary(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildPerformanceMetric('Win Rate', '73%', Colors.green, theme),
                _buildPerformanceMetric('Avg Return', '+12.5%', Colors.blue, theme),
                _buildPerformanceMetric('Total Trades', '186', theme.colorScheme.primary, theme),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Icon(Icons.trending_up, color: Colors.green, size: 20),
                const SizedBox(width: 8),
                Text(
                  'AI performance improved 15% this month',
                  style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationCard(
    String type,
    String symbol,
    String strike,
    String expiry,
    String confidence,
    String reason,
    ThemeData theme,
  ) {
    final isCall = type == 'Call';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isCall ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    type,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isCall ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text('$symbol \$${strike}', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(color: theme.colorScheme.primaryContainer, borderRadius: BorderRadius.circular(4)),
                  child: Text(
                    confidence,
                    style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.primary, fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('Expires: $expiry', style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
            const SizedBox(height: 8),
            Text(reason, style: theme.textTheme.bodyMedium),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(child: OutlinedButton(onPressed: () {}, child: const Text('View Details'))),
                const SizedBox(width: 8),
                Expanded(child: ElevatedButton(onPressed: () {}, child: const Text('Trade'))),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssistantCard(String name, String description, String status, IconData icon, ThemeData theme) {
    final isActive = status == 'Active';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: theme.colorScheme.primary, size: 24),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: isActive ? Colors.green.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    status,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isActive ? Colors.green : Colors.orange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(name, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Expanded(
              child: Text(description, style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () {},
                style: OutlinedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 8)),
                child: Text(isActive ? 'Configure' : 'Activate'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetric(String label, String value, Color color, ThemeData theme) {
    return Column(
      children: [
        Text(value, style: theme.textTheme.titleLarge?.copyWith(color: color, fontWeight: FontWeight.bold)),
        Text(label, style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
      ],
    );
  }
}
