import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../models/user_profile_simple.dart';
import '../../providers/riverpod/auth_provider.dart';
import '../../providers/riverpod/user_provider.dart';

class SettingsView extends ConsumerWidget {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final userProfile = ref.watch(userProfileNotifierProvider);
    final authNotifier = ref.read(authProvider.notifier);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Settings', style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
          const SizedBox(height: 20),

          // Profile Section
          _buildSection('Profile', [
            _buildSettingsItem(
              'Account Information',
              'Manage your account details',
              Icons.person,
              () => _showAccountDialog(context, userProfile.valueOrNull),
              theme,
            ),
            _buildSettingsItem('Subscription', 'Manage your subscription plan', Icons.card_membership, () {}, theme),
          ], theme),

          const SizedBox(height: 20),

          // Preferences Section
          _buildSection('Preferences', [
            _buildSettingsItem('Notifications', 'Configure notification settings', Icons.notifications, () {}, theme),
            _buildSettingsItem('Theme', 'Choose your app theme', Icons.palette, () {}, theme),
            _buildSettingsItem('Currency', 'Set your preferred currency', Icons.attach_money, () {}, theme),
            _buildSettingsItem('Market Hours', 'Configure market timezone', Icons.schedule, () {}, theme),
          ], theme),

          const SizedBox(height: 20),

          // Trading Section
          _buildSection('Trading', [
            _buildSettingsItem('Risk Management', 'Set your risk tolerance and limits', Icons.shield, () {}, theme),
            _buildSettingsItem('AI Settings', 'Configure AI assistant preferences', Icons.psychology, () {}, theme),
            _buildSettingsItem('Watchlist', 'Manage your watchlist', Icons.bookmark, () {}, theme),
          ], theme),

          const SizedBox(height: 20),

          // Support Section
          _buildSection('Support', [
            _buildSettingsItem('Help Center', 'Get help and support', Icons.help, () {}, theme),
            _buildSettingsItem('Feedback', 'Send us your feedback', Icons.feedback, () {}, theme),
            _buildSettingsItem('About', 'App version and information', Icons.info, () {}, theme),
          ], theme),

          const SizedBox(height: 20),

          // Logout Section
          _buildSection('Account', [
            _buildSettingsItem(
              'Sign Out',
              'Sign out of your account',
              Icons.logout,
              () => _showLogoutDialog(context, authNotifier),
              theme,
              isDestructive: true,
            ),
          ], theme),

          const SizedBox(height: 40),

          // App Version
          Center(
            child: Text(
              'Options AI v1.0.0',
              style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> items, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: theme.colorScheme.primary)),
        const SizedBox(height: 8),
        Card(
          child: Column(
            children:
                items.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  return Column(children: [item, if (index < items.length - 1) const Divider(height: 1)]);
                }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsItem(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
    ThemeData theme, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(icon, color: isDestructive ? Colors.red : theme.colorScheme.primary),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(color: isDestructive ? Colors.red : null, fontWeight: FontWeight.w500),
      ),
      subtitle: Text(subtitle, style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
      trailing: Icon(Icons.arrow_forward_ios, size: 16, color: theme.colorScheme.onSurfaceVariant),
      onTap: onTap,
    );
  }

  void _showAccountDialog(BuildContext context, UserProfile? profile) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Account Information'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Email: ${profile?.email ?? 'Not available'}'),
                const SizedBox(height: 8),
                Text('Name: ${profile?.fullName ?? 'Not set'}'),
                const SizedBox(height: 8),
                Text('Member since: ${profile?.createdAt?.toString().split(' ')[0] ?? 'Unknown'}'),
                const SizedBox(height: 8),
                Text('Subscription: ${profile?.subscriptionTier ?? 'Free'}'),
              ],
            ),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Close')),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // TODO: Navigate to edit profile screen
                },
                child: const Text('Edit'),
              ),
            ],
          ),
    );
  }

  void _showLogoutDialog(BuildContext context, Auth authNotifier) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sign Out'),
            content: const Text('Are you sure you want to sign out?'),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
              TextButton(
                onPressed: () async {
                  try {
                    await authNotifier.signOut();
                    if (context.mounted) {
                      Navigator.of(context).pop();
                      context.go('/login');
                    }
                  } catch (e) {
                    if (context.mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Error signing out: $e'), backgroundColor: Theme.of(context).colorScheme.error),
                      );
                    }
                  }
                },
                child: const Text('Sign Out'),
              ),
            ],
          ),
    );
  }
}
