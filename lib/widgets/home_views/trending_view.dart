import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class TrendingView extends ConsumerWidget {
  const TrendingView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Trending Now', style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),

          // Trending Stocks
          _buildSection('Most Active Stocks', _buildTrendingStocks(theme), theme),

          const SizedBox(height: 20),

          // Trending Options
          _buildSection('Hot Options', _buildTrendingOptions(theme), theme),

          const SizedBox(height: 20),

          // Market Movers
          _buildSection('Market Movers', _buildMarketMovers(theme), theme),
        ],
      ),
    );
  }

  Widget _buildSection(String title, Widget content, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  Widget _buildTrendingStocks(ThemeData theme) {
    final stocks = [
      {'symbol': 'AAPL', 'price': '\$175.25', 'change': '+2.5%', 'volume': '45.2M'},
      {'symbol': 'TSLA', 'price': '\$245.80', 'change': '+4.2%', 'volume': '38.7M'},
      {'symbol': 'NVDA', 'price': '\$485.60', 'change': '+1.8%', 'volume': '42.1M'},
      {'symbol': 'MSFT', 'price': '\$420.15', 'change': '+1.2%', 'volume': '28.9M'},
    ];

    return Card(
      child: Column(
        children:
            stocks.asMap().entries.map((entry) {
              final index = entry.key;
              final stock = entry.value;
              return Column(
                children: [
                  _buildStockItem(stock['symbol']!, stock['price']!, stock['change']!, stock['volume']!, theme),
                  if (index < stocks.length - 1) const Divider(height: 1),
                ],
              );
            }).toList(),
      ),
    );
  }

  Widget _buildTrendingOptions(ThemeData theme) {
    final options = [
      {'symbol': 'AAPL 175C', 'expiry': '03/15', 'volume': '12.5K', 'iv': '25.8%'},
      {'symbol': 'TSLA 250C', 'expiry': '03/22', 'volume': '8.9K', 'iv': '45.2%'},
      {'symbol': 'SPY 415P', 'expiry': '03/08', 'volume': '15.2K', 'iv': '18.9%'},
      {'symbol': 'QQQ 375C', 'expiry': '03/15', 'volume': '7.8K', 'iv': '22.4%'},
    ];

    return Card(
      child: Column(
        children:
            options.asMap().entries.map((entry) {
              final index = entry.key;
              final option = entry.value;
              return Column(
                children: [
                  _buildOptionItem(option['symbol']!, option['expiry']!, option['volume']!, option['iv']!, theme),
                  if (index < options.length - 1) const Divider(height: 1),
                ],
              );
            }).toList(),
      ),
    );
  }

  Widget _buildMarketMovers(ThemeData theme) {
    return Row(
      children: [
        Expanded(child: _buildMoverCard('Top Gainer', 'NVDA', '+8.5%', true, theme)),
        const SizedBox(width: 12),
        Expanded(child: _buildMoverCard('Top Loser', 'NFLX', '-3.2%', false, theme)),
      ],
    );
  }

  Widget _buildStockItem(String symbol, String price, String change, String volume, ThemeData theme) {
    final isPositive = change.startsWith('+');

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(flex: 2, child: Text(symbol, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold))),
          Expanded(flex: 2, child: Text(price, style: theme.textTheme.bodyLarge, textAlign: TextAlign.right)),
          Expanded(
            flex: 2,
            child: Text(
              change,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isPositive ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.right,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              volume,
              style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionItem(String symbol, String expiry, String volume, String iv, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(symbol, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                Text('Exp: $expiry', style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(volume, style: theme.textTheme.bodyMedium),
                Text('Volume', style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(iv, style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600)),
                Text('IV', style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMoverCard(String title, String symbol, String change, bool isPositive, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(title, style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
            const SizedBox(height: 8),
            Text(symbol, style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Text(
              change,
              style: theme.textTheme.titleMedium?.copyWith(
                color: isPositive ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
