import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class NewsView extends ConsumerWidget {
  const NewsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Market News', style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),

          // Featured News
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                  child: Container(
                    height: 200,
                    width: double.infinity,
                    color: theme.colorScheme.surfaceVariant,
                    child: Icon(Icons.image, size: 64, color: theme.colorScheme.onSurfaceVariant),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Fed Signals Potential Rate Cut in Next Meeting',
                        style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Federal Reserve Chairman hints at monetary policy adjustment following latest inflation data...',
                        style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Icon(Icons.access_time, size: 16, color: theme.colorScheme.onSurfaceVariant),
                          const SizedBox(width: 4),
                          Text(
                            '2 hours ago',
                            style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          Text('Latest Updates', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
          const SizedBox(height: 12),

          // News List
          ..._buildNewsList(theme),
        ],
      ),
    );
  }

  List<Widget> _buildNewsList(ThemeData theme) {
    final newsItems = [
      {
        'title': 'Tech Stocks Rally on AI Optimism',
        'summary': 'Major tech companies see significant gains following positive AI developments.',
        'time': '4 hours ago',
        'category': 'Technology',
      },
      {
        'title': 'Options Volume Hits Record High',
        'summary': 'Unprecedented options trading activity observed across major indices.',
        'time': '6 hours ago',
        'category': 'Options',
      },
      {
        'title': 'Volatility Index Shows Market Uncertainty',
        'summary': 'VIX rises as investors weigh economic uncertainties and policy changes.',
        'time': '8 hours ago',
        'category': 'Market',
      },
      {
        'title': 'Earnings Season Expectations Rise',
        'summary': 'Analysts upgrade expectations for Q4 earnings across multiple sectors.',
        'time': '1 day ago',
        'category': 'Earnings',
      },
    ];

    return newsItems
        .map((item) => _buildNewsItem(item['title']!, item['summary']!, item['time']!, item['category']!, theme))
        .toList();
  }

  Widget _buildNewsItem(String title, String summary, String time, String category, ThemeData theme) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(color: theme.colorScheme.primaryContainer, borderRadius: BorderRadius.circular(4)),
                  child: Text(
                    category,
                    style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.primary, fontWeight: FontWeight.w600),
                  ),
                ),
                const Spacer(),
                Text(time, style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
              ],
            ),
            const SizedBox(height: 8),
            Text(title, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
            const SizedBox(height: 4),
            Text(summary, style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
          ],
        ),
      ),
    );
  }
}
