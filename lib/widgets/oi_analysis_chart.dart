import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/option_data.dart';
import 'package:intl/intl.dart';

class OIAnalysisChart extends StatelessWidget {
  final List<OptionData> data;
  final List<OptionData> historicalData;
  final double? atmStrike;

  const OIAnalysisChart({super.key, required this.data, required this.historicalData, this.atmStrike});

  @override
  Widget build(BuildContext context) {
    final formatter = NumberFormat("#,##0");

    // Filter data by option type and where OI > 0
    final ceData = data.where((option) => option.optionType.toLowerCase() == 'call' && option.oi > 0).toList();
    final peData = data.where((option) => option.optionType.toLowerCase() == 'put' && option.oi > 0).toList();

    // Sort data by strike price
    ceData.sort((a, b) => a.strikePrice.compareTo(b.strikePrice));
    peData.sort((a, b) => a.strikePrice.compareTo(b.strikePrice));

    // Find max OI for Y-axis scaling
    final allOIs = [...ceData.map((d) => d.oi), ...peData.map((d) => d.oi)];
    final maxOI = allOIs.isEmpty ? 1000.0 : allOIs.reduce((a, b) => a > b ? a : b).toDouble();

    // Calculate PCR (Put-Call Ratio)
    final totalCEOI = ceData.fold<int>(0, (sum, d) => sum + d.oi);
    final totalPEOI = peData.fold<int>(0, (sum, d) => sum + d.oi);
    final pcr = totalPEOI / (totalCEOI == 0 ? 1 : totalCEOI);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Open Interest Analysis', style: Theme.of(context).textTheme.titleLarge),
                Text(
                  'PCR: ${pcr.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(color: pcr > 1 ? Colors.green : Colors.red),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text('Total CE OI: ${formatter.format(totalCEOI)}', style: const TextStyle(color: Colors.green)),
                Text('Total PE OI: ${formatter.format(totalPEOI)}', style: const TextStyle(color: Colors.red)),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: BarChart(
                BarChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 60,
                        getTitlesWidget: (value, meta) {
                          return Text(formatter.format(value));
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          return Padding(padding: const EdgeInsets.only(top: 8.0), child: Text(value.toStringAsFixed(0)));
                        },
                      ),
                    ),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: true),
                  barGroups: _createBarGroups(ceData, peData, atmStrike),
                  extraLinesData:
                      atmStrike != null
                          ? ExtraLinesData(
                            verticalLines: [
                              VerticalLine(
                                x: atmStrike!,
                                color: Colors.grey,
                                strokeWidth: 1,
                                dashArray: [5, 5],
                                label: VerticalLineLabel(
                                  show: true,
                                  alignment: Alignment.topCenter,
                                  style: const TextStyle(color: Colors.grey),
                                  labelResolver: (line) => 'ATM',
                                ),
                              ),
                            ],
                          )
                          : null,
                  maxY: maxOI * 1.1, // Add 10% padding to max value
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Legend
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  children: [
                    Container(width: 16, height: 16, color: Colors.green.withValues(red: 76, green: 175, blue: 80, alpha: 178)),
                    const SizedBox(width: 8),
                    const Text('Call OI'),
                  ],
                ),
                const SizedBox(width: 32),
                Row(
                  children: [
                    Container(width: 16, height: 16, color: Colors.red.withValues(red: 244, green: 67, blue: 54, alpha: 178)),
                    const SizedBox(width: 8),
                    const Text('Put OI'),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<BarChartGroupData> _createBarGroups(List<OptionData> ceData, List<OptionData> peData, double? atmStrike) {
    final groups = <BarChartGroupData>[];

    // Create a map of all unique strike prices
    final Map<double, int> strikeMap = {};
    for (final option in [...ceData, ...peData]) {
      strikeMap[option.strikePrice] = option.strikePrice.toInt();
    }

    // Sort the strike prices
    final sortedStrikes = strikeMap.keys.toList()..sort();

    // Create bar groups for each strike price
    for (final strike in sortedStrikes) {
      final int index = strike.toInt();
      final bool isAtm = atmStrike != null && (strike - atmStrike).abs() < 0.01;

      // Find call and put options for this strike
      final callOption = ceData.firstWhere(
        (o) => (o.strikePrice - strike).abs() < 0.01,
        orElse:
            () => OptionData(
              timestamp: '',
              expiryDate: '',
              strikePrice: strike,
              optionType: 'call',
              spotPrice: 0,
              lastPrice: 0,
              previousClosePrice: 0,
              volume: 0,
              previousVolume: 0,
              oi: 0,
              previousOi: 0,
              topBidPrice: 0,
              topAskPrice: 0,
              topBidQuantity: 0,
              topAskQuantity: 0,
              iv: 0,
              delta: 0,
              gamma: 0,
              theta: 0,
              vega: 0,
            ),
      );

      final putOption = peData.firstWhere(
        (o) => (o.strikePrice - strike).abs() < 0.01,
        orElse:
            () => OptionData(
              timestamp: '',
              expiryDate: '',
              strikePrice: strike,
              optionType: 'put',
              spotPrice: 0,
              lastPrice: 0,
              previousClosePrice: 0,
              volume: 0,
              previousVolume: 0,
              oi: 0,
              previousOi: 0,
              topBidPrice: 0,
              topAskPrice: 0,
              topBidQuantity: 0,
              topAskQuantity: 0,
              iv: 0,
              delta: 0,
              gamma: 0,
              theta: 0,
              vega: 0,
            ),
      );

      groups.add(
        BarChartGroupData(
          x: index,
          barRods: [
            BarChartRodData(
              toY: callOption.oi.toDouble(),
              color: Colors.green.withValues(red: 76, green: 175, blue: 80, alpha: isAtm ? 255 : 178),
              width: 8,
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(3), topRight: Radius.circular(3)),
            ),
            BarChartRodData(
              toY: putOption.oi.toDouble(),
              color: Colors.red.withValues(red: 244, green: 67, blue: 54, alpha: isAtm ? 255 : 178),
              width: 8,
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(3), topRight: Radius.circular(3)),
            ),
          ],
        ),
      );
    }

    return groups;
  }
}
