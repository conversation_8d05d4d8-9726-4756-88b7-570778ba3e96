import 'package:flutter/material.dart';
import 'package:data_table_2/data_table_2.dart';
import '../models/option_data.dart';
import 'package:intl/intl.dart';

class OptionChainGrid extends StatelessWidget {
  final String expiryDate;
  final List<OptionData> data;

  const OptionChainGrid({super.key, required this.expiryDate, required this.data});

  @override
  Widget build(BuildContext context) {
    final formatter = NumberFormat("#,##0.00");

    // Create a map of strike prices to organize call and put data
    final Map<double, Map<String, OptionData>> organizedData = {};

    // Process data to organize by strike price and option type
    for (final option in data) {
      if (!organizedData.containsKey(option.strikePrice)) {
        organizedData[option.strikePrice] = {};
      }

      if (option.optionType.toLowerCase() == 'call') {
        organizedData[option.strikePrice]!['call'] = option;
      } else if (option.optionType.toLowerCase() == 'put') {
        organizedData[option.strikePrice]!['put'] = option;
      }
    }

    // Sort strike prices
    final sortedStrikes = organizedData.keys.toList()..sort();

    // Create rows for the data table
    final rows =
        sortedStrikes.map((strike) {
          final callOption = organizedData[strike]?['call'];
          final putOption = organizedData[strike]?['put'];

          return DataRow2(
            cells: [
              DataCell(Text(formatter.format(strike))),
              DataCell(Text(callOption != null ? '${formatter.format(callOption.iv * 100)}%' : '-')),
              DataCell(Text(callOption != null ? formatter.format(callOption.oi.toDouble()) : '-')),
              DataCell(Text(callOption != null ? formatter.format(callOption.lastPrice) : '-')),
              DataCell(Text(putOption != null ? formatter.format(putOption.lastPrice) : '-')),
              DataCell(Text(putOption != null ? formatter.format(putOption.oi.toDouble()) : '-')),
              DataCell(Text(putOption != null ? '${formatter.format(putOption.iv * 100)}%' : '-')),
            ],
          );
        }).toList();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: DataTable2(
        columns: const [
          DataColumn2(label: Text('Strike'), size: ColumnSize.M),
          DataColumn2(label: Text('CE IV'), size: ColumnSize.M),
          DataColumn2(label: Text('CE OI'), size: ColumnSize.M),
          DataColumn2(label: Text('CE LTP'), size: ColumnSize.M),
          DataColumn2(label: Text('PE LTP'), size: ColumnSize.M),
          DataColumn2(label: Text('PE OI'), size: ColumnSize.M),
          DataColumn2(label: Text('PE IV'), size: ColumnSize.M),
        ],
        rows: rows,
      ),
    );
  }
}
