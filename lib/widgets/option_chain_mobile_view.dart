import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import '../providers/riverpod/options_provider.dart';
import '../models/option_data.dart';

class OptionChainMobileView extends ConsumerWidget {
  final bool showFilters;

  const OptionChainMobileView({super.key, this.showFilters = true});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final optionsState = ref.watch(optionsProvider);
    final optionsNotifier = ref.read(optionsProvider.notifier);

    // Formatters for numbers
    final formatter = NumberFormat("#,##0.00");
    final compactFormatter = NumberFormat.compact();

    // Get ATM strike
    final atmStrike = optionsNotifier.getAtmStrike();

    // Find the spot price from first option or use ATM strike
    final spotPrice = optionsState.optionChain.isNotEmpty ? optionsState.optionChain.first.spotPrice : atmStrike;

    // Process data
    if (optionsState.optionChain.isEmpty) {
      return const Center(child: Text('Loading data...'));
    }

    // Organize data by strike prices
    final Map<double, Map<String, OptionData>> organizedData = {};

    for (final option in optionsState.optionChain) {
      if (!organizedData.containsKey(option.strikePrice)) {
        organizedData[option.strikePrice] = {};
      }

      if (option.optionType.toLowerCase() == 'ce') {
        organizedData[option.strikePrice]!['ce'] = option;
      } else if (option.optionType.toLowerCase() == 'pe') {
        organizedData[option.strikePrice]!['pe'] = option;
      }
    }

    // Sort strike prices
    final sortedStrikes = organizedData.keys.toList()..sort();

    // Find nearby strikes (within ±5% of spot price)
    final nearbyStrikes =
        sortedStrikes.where((strike) {
          final percent = (strike - spotPrice).abs() / spotPrice;
          return percent <= 0.05; // Within 5%
        }).toList();

    return Column(
      children: [
        // Current price indicator
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Current Price: ${formatter.format(spotPrice)}', style: const TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(width: 8),
              Icon(Icons.arrow_downward, color: Theme.of(context).colorScheme.primary, size: 16),
            ],
          ),
        ),

        // Filter controls (if enabled)
        if (showFilters)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: SegmentedButton<String>(
                    segments: const [
                      ButtonSegment(value: 'nearby', label: Text('Nearby')),
                      ButtonSegment(value: 'all', label: Text('All')),
                    ],
                    selected: {'nearby'},
                    onSelectionChanged: (selected) {
                      // In a real app, this would filter the view
                    },
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: () {
                    // In a real app, this would show more filter options
                  },
                ),
              ],
            ),
          ),

        // Option chain cards
        Expanded(
          child: ListView.builder(
            itemCount: nearbyStrikes.length,
            padding: const EdgeInsets.all(8.0),
            itemBuilder: (context, index) {
              final strike = nearbyStrikes[index];
              final callOption = organizedData[strike]?['ce'];
              final putOption = organizedData[strike]?['pe'];

              // Highlight ATM strike
              final bool isAtm = strike == atmStrike;

              // Calculate changes
              final callOIChange = callOption != null ? (callOption.oi - callOption.previousOi) : 0;
              final putOIChange = putOption != null ? (putOption.oi - putOption.previousOi) : 0;

              final callPriceChange =
                  callOption != null
                      ? (callOption.lastPrice - callOption.previousClosePrice) /
                          (callOption.previousClosePrice == 0 ? 1 : callOption.previousClosePrice)
                      : 0.0;

              final putPriceChange =
                  putOption != null
                      ? (putOption.lastPrice - putOption.previousClosePrice) /
                          (putOption.previousClosePrice == 0 ? 1 : putOption.previousClosePrice)
                      : 0.0;

              return Card(
                elevation: 2,
                margin: const EdgeInsets.symmetric(vertical: 4.0),
                color: isAtm ? Theme.of(context).colorScheme.primary.withOpacity(0.05) : null,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      // Strike price header
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        decoration: BoxDecoration(
                          color:
                              isAtm
                                  ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                                  : Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                        child: Center(
                          child: Text(
                            'Strike: ${strike.toInt()}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: isAtm ? Theme.of(context).colorScheme.primary : null,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Call and Put data in a row
                      Row(
                        children: [
                          // CALL side
                          Expanded(
                            child: _buildOptionDataColumn(
                              context,
                              'CALL',
                              callOption?.lastPrice ?? 0,
                              callPriceChange,
                              callOption?.iv ?? 0,
                              callOption?.oi ?? 0,
                              callOIChange,
                              formatter,
                              compactFormatter,
                              Colors.blue,
                            ),
                          ),

                          // Vertical divider
                          Container(height: 80, width: 1, color: Theme.of(context).dividerColor),

                          // PUT side
                          Expanded(
                            child: _buildOptionDataColumn(
                              context,
                              'PUT',
                              putOption?.lastPrice ?? 0,
                              putPriceChange,
                              putOption?.iv ?? 0,
                              putOption?.oi ?? 0,
                              putOIChange,
                              formatter,
                              compactFormatter,
                              Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOptionDataColumn(
    BuildContext context,
    String title,
    double price,
    double priceChange,
    double iv,
    int oi,
    int oiChange,
    NumberFormat formatter,
    NumberFormat compactFormatter,
    Color accentColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Option type header
        Text(title, style: TextStyle(fontWeight: FontWeight.bold, color: accentColor, fontSize: 12)),
        const SizedBox(height: 4),

        // Price and change
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(formatter.format(price), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14)),
            const SizedBox(width: 4),
            _buildChangeIndicator(context, priceChange),
          ],
        ),

        // IV
        Text('IV: ${(iv * 100).toStringAsFixed(1)}%', style: const TextStyle(fontSize: 12)),

        // OI and change
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('OI: ${compactFormatter.format(oi)}', style: const TextStyle(fontSize: 12)),
            const SizedBox(width: 4),
            _buildChangeIndicator(context, oiChange.toDouble(), isPercentage: false),
          ],
        ),
      ],
    );
  }

  Widget _buildChangeIndicator(BuildContext context, double change, {bool isPercentage = true}) {
    final color = change > 0 ? Colors.green : (change < 0 ? Colors.red : Colors.grey);
    final changeText =
        isPercentage
            ? '${change >= 0 ? '+' : ''}${(change * 100).toStringAsFixed(1)}%'
            : '${change >= 0 ? '+' : ''}${change.toInt()}';

    return Text(changeText, style: TextStyle(color: color, fontSize: 10, fontWeight: FontWeight.bold));
  }
}
