import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/option_data.dart';

class IVS<PERSON><PERSON>hart extends StatelessWidget {
  final List<OptionData> data;
  final List<OptionData> historicalData;
  final double? atmStrike;

  const IVSmileChart({super.key, required this.data, required this.historicalData, this.atmStrike});

  @override
  Widget build(BuildContext context) {
    // Process latest data - filter by option type and where IV > 0
    final ceData = data.where((option) => option.optionType.toLowerCase() == 'call' && option.iv > 0).toList();
    final peData = data.where((option) => option.optionType.toLowerCase() == 'put' && option.iv > 0).toList();

    // Sort data by strike price
    ceData.sort((a, b) => a.strikePrice.compareTo(b.strikePrice));
    peData.sort((a, b) => a.strikePrice.compareTo(b.strikePrice));

    // Find min and max IV for Y-axis scaling
    final allIVs = [...ceData.map((d) => d.iv), ...peData.map((d) => d.iv)];

    // Add null checks for when there's no data
    final minIV = allIVs.isEmpty ? 0.0 : allIVs.reduce((a, b) => a < b ? a : b);
    final maxIV = allIVs.isEmpty ? 0.1 : allIVs.reduce((a, b) => a > b ? a : b);
    final ivRange = maxIV - minIV;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text('IV Smile Analysis', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            Expanded(
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        getTitlesWidget: (value, meta) {
                          // Convert decimal IV to percentage
                          return Text('${(value * 100).toStringAsFixed(1)}%');
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          return Padding(padding: const EdgeInsets.only(top: 8.0), child: Text(value.toStringAsFixed(0)));
                        },
                      ),
                    ),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    // Call Options (CE)
                    LineChartBarData(
                      spots: ceData.map((d) => FlSpot(d.strikePrice, d.iv)).toList(),
                      isCurved: true,
                      color: Colors.green,
                      dotData: const FlDotData(show: true),
                      belowBarData: BarAreaData(show: false),
                    ),
                    // Put Options (PE)
                    LineChartBarData(
                      spots: peData.map((d) => FlSpot(d.strikePrice, d.iv)).toList(),
                      isCurved: true,
                      color: Colors.red,
                      dotData: const FlDotData(show: true),
                      belowBarData: BarAreaData(show: false),
                    ),
                  ],
                  extraLinesData:
                      atmStrike != null
                          ? ExtraLinesData(
                            verticalLines: [
                              VerticalLine(
                                x: atmStrike!,
                                color: Colors.grey,
                                strokeWidth: 1,
                                dashArray: [5, 5],
                                label: VerticalLineLabel(
                                  show: true,
                                  alignment: Alignment.topCenter,
                                  style: const TextStyle(color: Colors.grey),
                                  labelResolver: (line) => 'ATM',
                                ),
                              ),
                            ],
                          )
                          : null,
                  minY: minIV - (ivRange * 0.1), // Add 10% padding
                  maxY: maxIV + (ivRange * 0.1), // Add 10% padding
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Legend
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  children: [
                    Container(width: 16, height: 16, color: Colors.green),
                    const SizedBox(width: 8),
                    const Text('Call Options'),
                  ],
                ),
                const SizedBox(width: 32),
                Row(
                  children: [
                    Container(width: 16, height: 16, color: Colors.red),
                    const SizedBox(width: 8),
                    const Text('Put Options'),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
