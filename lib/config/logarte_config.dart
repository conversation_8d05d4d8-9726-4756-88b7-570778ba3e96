import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:logarte/logarte.dart';

/// Global Logarte instance for debugging and logging
final Logarte logarte = Logarte(
  // Protect with password in production
  password: '2024',

  // Skip password in debug mode for easier development
  ignorePassword: kDebugMode,

  // Share network request logs
  onShare: (String content) {
    // For now, just log that sharing was requested
    // You can add share_plus package later if needed
    logarte.log('📤 Share requested: ${content.length} characters');
  },

  // Enable logs in IDE's debug console during development
  disableDebugConsoleLogs: false,

  // Custom shortcuts for development
  onRocketLongPressed: (context) {
    // Could be used to toggle theme mode or other debug actions
    logarte.log('🚀 Rocket long pressed - Debug action triggered');
  },

  onRocketDoubleTapped: (context) {
    // Could be used to change environment or other quick actions
    logarte.log('🚀 Rocket double tapped - Quick action triggered');
  },

  // Custom debug tab with app-specific tools
  customTab: const MarketMagicDebugTab(),
);

/// Custom debug tab with Market Magic AI specific debugging tools
class MarketMagicDebugTab extends StatefulWidget {
  const MarketMagicDebugTab({super.key});

  @override
  State<MarketMagicDebugTab> createState() => _MarketMagicDebugTabState();
}

class _MarketMagicDebugTabState extends State<MarketMagicDebugTab> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Market Magic AI Debug Tools', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),

          // Environment info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Environment', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Text('Debug Mode: ${kDebugMode ? 'ON' : 'OFF'}'),
                  Text('Web Platform: ${kIsWeb ? 'YES' : 'NO'}'),
                  Text('Release Mode: ${kReleaseMode ? 'ON' : 'OFF'}'),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Quick actions
          Card(
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Quick Actions', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),

                  ElevatedButton.icon(
                    onPressed: () {
                      logarte.log('🧹 Cache cleared manually from debug panel');
                    },
                    icon: const Icon(Icons.clear_all),
                    label: const Text('Clear Local Cache'),
                  ),

                  const SizedBox(height: 8),

                  ElevatedButton.icon(
                    onPressed: () {
                      logarte.log('📊 Test data generated from debug panel');
                    },
                    icon: const Icon(Icons.data_usage),
                    label: const Text('Generate Test Data'),
                  ),

                  const SizedBox(height: 8),

                  ElevatedButton.icon(
                    onPressed: () {
                      logarte.log('🔄 Force refresh triggered from debug panel');
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Force Refresh'),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // App info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('App Information', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  const Text('Version: 1.0.0+1'),
                  const Text('Build: Debug'),
                  Text('Platform: ${Theme.of(context).platform.name}'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
