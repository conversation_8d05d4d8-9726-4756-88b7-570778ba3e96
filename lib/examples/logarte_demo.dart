import 'package:flutter/material.dart';
import 'package:logarte/logarte.dart';
import '../config/logarte_config.dart';

/// Example widget showing how to use Logarte in your app
class LogarteExampleWidget extends StatelessWidget {
  const LogarteExampleWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Logarte Demo'),
        actions: [
          // Button to manually open debug console
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () {
              // Manual trigger for debug console
              logarte.openConsole(context);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text('Logarte Integration Demo', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),

            // Example buttons showing different log types
            ElevatedButton(
              onPressed: () {
                logarte.log('🎯 Basic log message created');
              },
              child: const Text('Log Basic Message'),
            ),

            const SizedBox(height: 10),

            ElevatedButton(
              onPressed: () {
                logarte.log('📊 User clicked on demo button');
                logarte.database(target: 'demo_table', value: 'SELECT * FROM demo_table WHERE id = 123', source: 'Demo Database');
              },
              child: const Text('Log Database Operation'),
            ),

            const SizedBox(height: 10),

            ElevatedButton(
              onPressed: () {
                _simulateApiCall();
              },
              child: const Text('Simulate API Call'),
            ),

            const SizedBox(height: 10),

            ElevatedButton(
              onPressed: () {
                logarte.log('⚠️ Warning: This is a test warning');
              },
              child: const Text('Log Warning'),
            ),

            const SizedBox(height: 10),

            ElevatedButton(
              onPressed: () {
                logarte.log('❌ Error: This is a test error');
              },
              child: const Text('Log Error'),
            ),

            const SizedBox(height: 20),

            // Hidden trigger demonstration
            LogarteMagicalTap(
              logarte: logarte,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(color: Colors.grey[200], borderRadius: BorderRadius.circular(8)),
                child: const Text(
                  '🤫 Secret Debug Trigger\n(Tap 10 times to open debug console)',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
            ),

            const SizedBox(height: 20),

            const Text('Tips:', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const Text(
              '• In debug mode, you\'ll see a floating rocket button\n'
              '• Tap the rocket to open the debug console\n'
              '• Check the Network tab for API calls\n'
              '• Use the Custom tab for app-specific tools\n'
              '• Logs are automatically organized by type',
            ),
          ],
        ),
      ),
    );
  }

  void _simulateApiCall() async {
    logarte.log('🔄 Starting API call simulation...');

    final stopwatch = Stopwatch()..start();

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    stopwatch.stop();

    // Log the simulated response
    logarte.log('✅ API call completed in ${stopwatch.elapsedMilliseconds}ms');
    logarte.database(target: 'api_endpoint', value: 'GET /api/options/chain', source: 'REST API');
  }
}
