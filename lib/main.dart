import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb, kDebugMode;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'config/env.dart';
import 'config/theme.dart';
import 'config/logarte_config.dart';
import 'router/app_router.dart';
import 'screens/settings_screen.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Load environment variables
  await dotenv.load(fileName: "assets/dotenv");

  // Initialize Supabase with fallback for production
  final supabaseUrl =
      kIsWeb
          ? const String.fromEnvironment('SUPABASE_URL', defaultValue: 'https://efnbpmrtjclzykbpxnzh.supabase.co')
          : Env.supabaseUrl;

  final supabaseAnonKey =
      kIsWeb
          ? const String.fromEnvironment(
            'SUPABASE_ANON_KEY',
            defaultValue:
                'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.PQfVfyhXR0lqLyu02TeWJJkg1ujGEiB0VtDBqyeTpMQ',
          )
          : Env.supabaseAnonKey;

  await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);

  // Initialize Logarte
  logarte.log('🚀 Market Magic AI starting up...');
  logarte.log('🌐 Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
  logarte.log('🔧 Debug mode: ${kDebugMode}');

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends HookConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the router from our AppRouter
    final router = AppRouter(ref).router;

    // Watch the theme mode from the settings provider
    final themeMode = ref.watch(themeModeProvider);

    return MaterialApp.router(
      title: 'Market Magic AI',
      theme: AppTheme.light,
      darkTheme: AppTheme.dark,
      themeMode: themeMode,
      debugShowCheckedModeBanner: false,
      routerConfig: router,
      // Add Logarte navigation observer to track route changes
      builder: (context, child) {
        // Attach Logarte floating button in debug mode
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (kDebugMode) {
            logarte.attach(context: context, visible: true);
          }
        });

        return child ?? const SizedBox.shrink();
      },
    );
  }
}
