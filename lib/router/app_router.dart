import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logarte/logarte.dart';

import '../config/logarte_config.dart';
import '../providers/riverpod/auth_provider.dart';
import '../providers/riverpod/user_provider.dart';
import '../screens/login_screen.dart';
import '../screens/enhanced_login_screen.dart';
import '../screens/onboarding_screen.dart';
import '../screens/enhanced_onboarding_screen.dart';
import '../screens/home_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/option_chain_view.dart';
import '../screens/iv_analysis_view.dart';
import '../screens/oi_analysis_view.dart';
import '../screens/main_layout.dart';
import '../screens/ai_agent_screen.dart';
import '../screens/settings_screen.dart';

class AppRouter {
  final WidgetRef ref;

  AppRouter(this.ref);

  GoRouter get router => GoRouter(
    initialLocation: '/home',
    debugLogDiagnostics: true,
    // Add Logarte navigation observer to track route changes
    observers: [LogarteNavigatorObserver(logarte)],
    // Use Riverpod to access auth state
    redirect: (context, state) {
      // Use the provider to check auth status
      final authState = ref.watch(authProvider);
      final userProfile = ref.watch(userProfileNotifierProvider);
      final isAuthenticated = authState.valueOrNull?.session != null;
      final isOnboarded = userProfile.valueOrNull?.isOnboarded ?? false;
      final isLoginRoute = state.matchedLocation == '/login';
      final isOnboardingRoute = state.matchedLocation == '/onboarding';

      // If not logged in and not on login page, go to login
      if (!isAuthenticated && !isLoginRoute) {
        return '/login';
      }

      // If logged in but not onboarded and not on onboarding page
      if (isAuthenticated && !isOnboarded && !isOnboardingRoute) {
        return '/onboarding';
      }

      // If logged in, onboarded, and on login/onboarding page, redirect to home
      if (isAuthenticated && isOnboarded && (isLoginRoute || isOnboardingRoute)) {
        return '/home';
      }

      // No redirect needed
      return null;
    },
    routes: [
      // Enhanced Login route without main layout
      GoRoute(path: '/login', name: 'login', builder: (context, state) => const EnhancedLoginScreen()),

      // Enhanced Onboarding route without main layout
      GoRoute(path: '/onboarding', name: 'onboarding', builder: (context, state) => const EnhancedOnboardingScreen()),

      // Legacy routes for backward compatibility
      GoRoute(path: '/legacy-login', name: 'legacy-login', builder: (context, state) => const LoginScreen()),
      GoRoute(path: '/legacy-onboarding', name: 'legacy-onboarding', builder: (context, state) => const OnboardingScreen()),

      // Main routes with shared layout
      ShellRoute(
        builder: (context, state, child) {
          // Pass the current route path to the layout
          return MainLayout(child: child, currentRoute: state.matchedLocation);
        },
        routes: [
          GoRoute(path: '/home', name: 'home', builder: (context, state) => const NewHomeScreen()),
          GoRoute(path: '/option-chain', name: 'option-chain', builder: (context, state) => const OptionChainView()),
          GoRoute(path: '/iv-analysis', name: 'iv-analysis', builder: (context, state) => const IVAnalysisView()),
          GoRoute(path: '/oi-analysis', name: 'oi-analysis', builder: (context, state) => const OIAnalysisView()),
          GoRoute(path: '/profile', name: 'profile', builder: (context, state) => const ProfileScreen()),
          GoRoute(path: '/ai-agent', name: 'ai-agent', builder: (context, state) => const AIAgentScreen()),
          GoRoute(path: '/settings', name: 'settings', builder: (context, state) => const SettingsScreen()),
          // Redirect root to home
          GoRoute(path: '/', redirect: (_, __) => '/home'),
        ],
      ),
    ],
    // Simplified approach - refresh whenever build is called which is often enough
    refreshListenable: _AuthRefreshNotifier(ref),
  );
}

// A simplified refresh notifier for auth changes
class _AuthRefreshNotifier extends ChangeNotifier {
  _AuthRefreshNotifier(this.ref) {
    ref.listenManual(authProvider, (_, __) {
      notifyListeners();
    });
  }

  final WidgetRef ref;
}
