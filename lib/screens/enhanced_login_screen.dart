import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';
import 'package:pinput/pinput.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/riverpod/auth_provider.dart';
import '../widgets/common/common_widgets.dart' as custom;

class EnhancedLoginScreen extends HookConsumerWidget {
  const EnhancedLoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final phoneController = useTextEditingController();
    final isLoading = useState(false);
    final authMode = useState(AuthMode.email); // email, phone, socialOnly
    final isLogin = useState(true); // true for login, false for signup
    final obscurePassword = useState(true);

    // Phone verification states
    final phoneVerificationId = useState<String?>(null);
    final phoneNumber = useState<String>('');
    final pinController = useTextEditingController();

    // Get the authentication provider
    final authNotifier = ref.read(authProvider.notifier);

    // Handle authentication result
    void handleAuthResult(dynamic result) async {
      if (result != null) {
        // Success - navigate to home or onboarding
        if (context.mounted) {
          context.go('/home');
        }
      }
    }

    // Email Authentication
    Future<void> submitEmailForm() async {
      if (!formKey.currentState!.validate()) return;

      isLoading.value = true;
      try {
        if (isLogin.value) {
          final result = await authNotifier.signIn(email: emailController.text.trim(), password: passwordController.text);
          handleAuthResult(result);
        } else {
          final result = await authNotifier.signUp(email: emailController.text.trim(), password: passwordController.text);
          handleAuthResult(result);
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(e.toString()),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } finally {
        isLoading.value = false;
      }
    }

    // Google Authentication
    Future<void> signInWithGoogle() async {
      isLoading.value = true;
      try {
        // This would use the enhanced auth service
        // final result = await enhancedAuth.signInWithGoogle();
        // For now, use the existing auth provider
        final result = await authNotifier.signIn(email: '<EMAIL>', password: 'temp-password');
        handleAuthResult(result);
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Google Sign-In failed: ${e.toString()}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      } finally {
        isLoading.value = false;
      }
    }

    // Apple Authentication
    Future<void> signInWithApple() async {
      isLoading.value = true;
      try {
        // This would use the enhanced auth service
        // final result = await enhancedAuth.signInWithApple();
        // For now, show placeholder
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Apple Sign-In coming soon!')));
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Apple Sign-In failed: ${e.toString()}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      } finally {
        isLoading.value = false;
      }
    }

    // Phone Authentication
    Future<void> sendPhoneCode() async {
      if (phoneController.text.trim().isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter your phone number')));
        return;
      }

      isLoading.value = true;
      phoneNumber.value = phoneController.text.trim();

      try {
        // This would use the enhanced auth service for phone verification
        // For now, simulate sending code
        await Future.delayed(const Duration(seconds: 2));
        phoneVerificationId.value = 'dummy-verification-id';

        if (context.mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('Verification code sent!'), backgroundColor: Colors.green));
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to send code: ${e.toString()}'), backgroundColor: Theme.of(context).colorScheme.error),
          );
        }
      } finally {
        isLoading.value = false;
      }
    }

    Future<void> verifyPhoneCode() async {
      if (pinController.text.length != 6) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please enter the 6-digit code')));
        return;
      }

      isLoading.value = true;
      try {
        // This would use the enhanced auth service
        // final result = await enhancedAuth.verifyPhoneCode(
        //   verificationId: phoneVerificationId.value!,
        //   smsCode: pinController.text,
        // );

        // For now, simulate verification
        await Future.delayed(const Duration(seconds: 1));
        handleAuthResult('success');
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Verification failed: ${e.toString()}'), backgroundColor: Theme.of(context).colorScheme.error),
          );
        }
      } finally {
        isLoading.value = false;
      }
    }

    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Scaffold(
      body: Stack(
        children: [
          // Animated gradient background
          AnimatedContainer(
            duration: const Duration(seconds: 10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  theme.colorScheme.primary.withOpacity(0.8),
                  theme.colorScheme.secondary.withOpacity(0.6),
                  theme.colorScheme.tertiary.withOpacity(0.4),
                ],
                stops: const [0.0, 0.6, 1.0],
              ),
            ),
          ),

          // Floating circles for background decoration
          _buildFloatingCircles(),

          // Main content
          SafeArea(
            child: Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: isSmallScreen ? double.infinity : 400),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Enhanced Logo
                      _buildEnhancedLogo(theme),

                      const SizedBox(height: 32),

                      // App title
                      FadeInUp(
                        duration: const Duration(milliseconds: 800),
                        delay: const Duration(milliseconds: 200),
                        child: Text(
                          'Options AI',
                          style: GoogleFonts.poppins(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [Shadow(color: Colors.black.withOpacity(0.3), offset: const Offset(0, 2), blurRadius: 4)],
                          ),
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Subtitle
                      FadeInUp(
                        duration: const Duration(milliseconds: 800),
                        delay: const Duration(milliseconds: 400),
                        child: Text(
                          'Your AI-Powered Trading Platform',
                          style: theme.textTheme.titleMedium?.copyWith(color: Colors.white.withOpacity(0.9), fontSize: 18),
                        ),
                      ),

                      const SizedBox(height: 48),

                      // Authentication Mode Selector
                      _buildAuthModeSelector(authMode, theme),

                      const SizedBox(height: 24),

                      // Authentication Forms
                      _buildAuthenticationForms(
                        authMode: authMode,
                        isLogin: isLogin,
                        formKey: formKey,
                        emailController: emailController,
                        passwordController: passwordController,
                        phoneController: phoneController,
                        pinController: pinController,
                        phoneVerificationId: phoneVerificationId,
                        isLoading: isLoading,
                        obscurePassword: obscurePassword,
                        theme: theme,
                        submitEmailForm: submitEmailForm,
                        sendPhoneCode: sendPhoneCode,
                        verifyPhoneCode: verifyPhoneCode,
                        signInWithGoogle: signInWithGoogle,
                        signInWithApple: signInWithApple,
                      ),

                      const SizedBox(height: 32),

                      // Social Authentication Buttons (always visible)
                      if (authMode.value != AuthMode.socialOnly) ...[
                        _buildSocialAuthButtons(
                          signInWithGoogle: signInWithGoogle,
                          signInWithApple: signInWithApple,
                          isLoading: isLoading,
                          theme: theme,
                        ),
                        const SizedBox(height: 32),
                      ],

                      // Toggle login/signup for email mode
                      if (authMode.value == AuthMode.email) _buildLoginSignupToggle(isLogin, theme),

                      const SizedBox(height: 24),

                      // Footer
                      FadeIn(
                        duration: const Duration(milliseconds: 600),
                        delay: const Duration(milliseconds: 1800),
                        child: Text(
                          '© ${DateTime.now().year} Options AI • Secure Trading Platform',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.white.withOpacity(0.6), fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedLogo(ThemeData theme) {
    return BounceInDown(
      duration: const Duration(milliseconds: 1000),
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.white.withOpacity(0.95), Colors.white.withOpacity(0.85)],
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(color: Colors.black.withOpacity(0.2), blurRadius: 20, offset: const Offset(0, 10)),
            BoxShadow(color: theme.colorScheme.primary.withOpacity(0.3), blurRadius: 30, offset: const Offset(0, 5)),
          ],
        ),
        child: const Icon(Icons.trending_up_rounded, size: 60, color: Colors.blue),
      ),
    );
  }

  Widget _buildFloatingCircles() {
    return Stack(
      children: [
        Positioned(
          top: -100,
          right: -100,
          child: FadeIn(
            duration: const Duration(milliseconds: 2000),
            child: Container(
              width: 300,
              height: 300,
              decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.white.withOpacity(0.1)),
            ),
          ),
        ),
        Positioned(
          bottom: -150,
          left: -150,
          child: FadeIn(
            duration: const Duration(milliseconds: 2500),
            child: Container(
              width: 400,
              height: 400,
              decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.white.withOpacity(0.05)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAuthModeSelector(ValueNotifier<AuthMode> authMode, ThemeData theme) {
    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      delay: const Duration(milliseconds: 600),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Expanded(
              child: _buildModeButton(
                'Email',
                Icons.email_outlined,
                authMode.value == AuthMode.email,
                () => authMode.value = AuthMode.email,
                theme,
              ),
            ),
            Expanded(
              child: _buildModeButton(
                'Phone',
                Icons.phone_outlined,
                authMode.value == AuthMode.phone,
                () => authMode.value = AuthMode.phone,
                theme,
              ),
            ),
            Expanded(
              child: _buildModeButton(
                'Social',
                Icons.group_outlined,
                authMode.value == AuthMode.socialOnly,
                () => authMode.value = AuthMode.socialOnly,
                theme,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModeButton(String label, IconData icon, bool isSelected, VoidCallback onTap, ThemeData theme) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white.withOpacity(0.2) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAuthenticationForms({
    required ValueNotifier<AuthMode> authMode,
    required ValueNotifier<bool> isLogin,
    required GlobalKey<FormState> formKey,
    required TextEditingController emailController,
    required TextEditingController passwordController,
    required TextEditingController phoneController,
    required TextEditingController pinController,
    required ValueNotifier<String?> phoneVerificationId,
    required ValueNotifier<bool> isLoading,
    required ValueNotifier<bool> obscurePassword,
    required ThemeData theme,
    required VoidCallback submitEmailForm,
    required VoidCallback sendPhoneCode,
    required VoidCallback verifyPhoneCode,
    required VoidCallback signInWithGoogle,
    required VoidCallback signInWithApple,
  }) {
    switch (authMode.value) {
      case AuthMode.email:
        return _buildEmailForm(
          formKey: formKey,
          emailController: emailController,
          passwordController: passwordController,
          isLogin: isLogin,
          isLoading: isLoading,
          obscurePassword: obscurePassword,
          theme: theme,
          onSubmit: submitEmailForm,
        );
      case AuthMode.phone:
        return _buildPhoneForm(
          phoneController: phoneController,
          pinController: pinController,
          phoneVerificationId: phoneVerificationId,
          isLoading: isLoading,
          theme: theme,
          sendPhoneCode: sendPhoneCode,
          verifyPhoneCode: verifyPhoneCode,
        );
      case AuthMode.socialOnly:
        return _buildSocialOnlyForm(
          signInWithGoogle: signInWithGoogle,
          signInWithApple: signInWithApple,
          isLoading: isLoading,
          theme: theme,
        );
    }
  }

  Widget _buildEmailForm({
    required GlobalKey<FormState> formKey,
    required TextEditingController emailController,
    required TextEditingController passwordController,
    required ValueNotifier<bool> isLogin,
    required ValueNotifier<bool> isLoading,
    required ValueNotifier<bool> obscurePassword,
    required ThemeData theme,
    required VoidCallback onSubmit,
  }) {
    return FadeInUp(
      duration: const Duration(milliseconds: 800),
      delay: const Duration(milliseconds: 800),
      child: custom.GlassCard(
        padding: const EdgeInsets.all(32),
        child: Form(
          key: formKey,
          child: Column(
            children: [
              // Email field
              SlideInLeft(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 1000),
                child: _buildTextField(
                  controller: emailController,
                  label: 'Email',
                  icon: Icons.email_outlined,
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your email';
                    }
                    if (!value.contains('@')) {
                      return 'Please enter a valid email';
                    }
                    return null;
                  },
                  theme: theme,
                ),
              ),

              const SizedBox(height: 24),

              // Password field
              SlideInRight(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 1200),
                child: _buildTextField(
                  controller: passwordController,
                  label: 'Password',
                  icon: Icons.lock_outline,
                  obscureText: obscurePassword.value,
                  suffixIcon: IconButton(
                    icon: Icon(
                      obscurePassword.value ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                      color: Colors.white.withOpacity(0.7),
                    ),
                    onPressed: () => obscurePassword.value = !obscurePassword.value,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your password';
                    }
                    if (value.length < 6) {
                      return 'Password must be at least 6 characters';
                    }
                    return null;
                  },
                  theme: theme,
                ),
              ),

              const SizedBox(height: 16),

              // Forgot password
              if (isLogin.value)
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () {
                      // Handle forgot password
                    },
                    child: Text(
                      'Forgot Password?',
                      style: TextStyle(color: Colors.white.withOpacity(0.8), fontWeight: FontWeight.w500),
                    ),
                  ),
                ),

              const SizedBox(height: 32),

              // Submit button
              ZoomIn(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 1400),
                child: custom.AnimatedButton(
                  text: isLogin.value ? 'Sign In' : 'Create Account',
                  onPressed: isLoading.value ? null : onSubmit,
                  isLoading: isLoading.value,
                  width: double.infinity,
                  height: 56,
                  backgroundColor: Colors.white,
                  textColor: theme.colorScheme.primary,
                  borderRadius: 16,
                  icon: isLogin.value ? Icons.login_rounded : Icons.person_add_rounded,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhoneForm({
    required TextEditingController phoneController,
    required TextEditingController pinController,
    required ValueNotifier<String?> phoneVerificationId,
    required ValueNotifier<bool> isLoading,
    required ThemeData theme,
    required VoidCallback sendPhoneCode,
    required VoidCallback verifyPhoneCode,
  }) {
    return FadeInUp(
      duration: const Duration(milliseconds: 800),
      delay: const Duration(milliseconds: 800),
      child: custom.GlassCard(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            if (phoneVerificationId.value == null) ...[
              // Phone number input
              _buildTextField(
                controller: phoneController,
                label: 'Phone Number',
                icon: Icons.phone_outlined,
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your phone number';
                  }
                  return null;
                },
                theme: theme,
              ),

              const SizedBox(height: 32),

              // Send code button
              custom.AnimatedButton(
                text: 'Send Verification Code',
                onPressed: isLoading.value ? null : sendPhoneCode,
                isLoading: isLoading.value,
                width: double.infinity,
                height: 56,
                backgroundColor: Colors.white,
                textColor: theme.colorScheme.primary,
                borderRadius: 16,
                icon: Icons.sms_outlined,
              ),
            ] else ...[
              // PIN input
              Text(
                'Enter the verification code sent to your phone',
                style: theme.textTheme.bodyMedium?.copyWith(color: Colors.white.withOpacity(0.9)),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              Pinput(
                controller: pinController,
                length: 6,
                defaultPinTheme: PinTheme(
                  width: 50,
                  height: 50,
                  textStyle: const TextStyle(fontSize: 20, color: Colors.white),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white.withOpacity(0.3)),
                  ),
                ),
                focusedPinTheme: PinTheme(
                  width: 50,
                  height: 50,
                  textStyle: const TextStyle(fontSize: 20, color: Colors.white),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white),
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Verify button
              custom.AnimatedButton(
                text: 'Verify Code',
                onPressed: isLoading.value ? null : verifyPhoneCode,
                isLoading: isLoading.value,
                width: double.infinity,
                height: 56,
                backgroundColor: Colors.white,
                textColor: theme.colorScheme.primary,
                borderRadius: 16,
                icon: Icons.verified_outlined,
              ),

              const SizedBox(height: 16),

              // Resend code
              TextButton(
                onPressed: () {
                  phoneVerificationId.value = null;
                  pinController.clear();
                },
                child: Text('Resend Code', style: TextStyle(color: Colors.white.withOpacity(0.8))),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSocialOnlyForm({
    required VoidCallback signInWithGoogle,
    required VoidCallback signInWithApple,
    required ValueNotifier<bool> isLoading,
    required ThemeData theme,
  }) {
    return FadeInUp(
      duration: const Duration(milliseconds: 800),
      delay: const Duration(milliseconds: 800),
      child: custom.GlassCard(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Text(
              'Choose your preferred sign-in method',
              style: theme.textTheme.bodyLarge?.copyWith(color: Colors.white.withOpacity(0.9)),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            _buildSocialAuthButtons(
              signInWithGoogle: signInWithGoogle,
              signInWithApple: signInWithApple,
              isLoading: isLoading,
              theme: theme,
              vertical: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialAuthButtons({
    required VoidCallback signInWithGoogle,
    required VoidCallback signInWithApple,
    required ValueNotifier<bool> isLoading,
    required ThemeData theme,
    bool vertical = false,
  }) {
    final children = [
      // Google Sign In
      _buildSocialButton(
        'Continue with Google',
        Icons.g_mobiledata,
        Colors.white,
        theme.colorScheme.primary,
        signInWithGoogle,
        isLoading.value,
      ),

      if (vertical) const SizedBox(height: 16) else const SizedBox(width: 16),

      // Apple Sign In
      _buildSocialButton('Continue with Apple', Icons.apple, Colors.black, Colors.white, signInWithApple, isLoading.value),
    ];

    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      delay: const Duration(milliseconds: 1600),
      child: vertical ? Column(children: children) : Row(children: children.map((child) => Expanded(child: child)).toList()),
    );
  }

  Widget _buildSocialButton(
    String text,
    IconData icon,
    Color backgroundColor,
    Color textColor,
    VoidCallback onPressed,
    bool isLoading,
  ) {
    return custom.AnimatedButton(
      text: text,
      onPressed: isLoading ? null : onPressed,
      isLoading: false, // Don't show loading on individual social buttons
      backgroundColor: backgroundColor,
      textColor: textColor,
      borderRadius: 12,
      icon: icon,
      height: 50,
    );
  }

  Widget _buildLoginSignupToggle(ValueNotifier<bool> isLogin, ThemeData theme) {
    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      delay: const Duration(milliseconds: 1600),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            isLogin.value ? "Don't have an account? " : "Already have an account? ",
            style: TextStyle(color: Colors.white.withOpacity(0.8), fontSize: 16),
          ),
          GestureDetector(
            onTap: () => isLogin.value = !isLogin.value,
            child: Text(
              isLogin.value ? 'Sign Up' : 'Sign In',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                decoration: TextDecoration.underline,
                decorationColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String? Function(String?) validator,
    required ThemeData theme,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(color: Colors.white.withOpacity(0.8)),
        hintText: 'Enter your ${label.toLowerCase()}',
        hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
        prefixIcon: Icon(icon, color: Colors.white.withOpacity(0.8)),
        suffixIcon: suffixIcon,
        filled: true,
        fillColor: Colors.white.withOpacity(0.1),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: Colors.white, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
      ),
      validator: validator,
    );
  }
}

enum AuthMode { email, phone, socialOnly }
