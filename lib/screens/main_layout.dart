import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../providers/riverpod/options_provider.dart';

class MainLayout extends ConsumerStatefulWidget {
  final Widget child;
  final String currentRoute;

  const MainLayout({super.key, required this.child, required this.currentRoute});

  @override
  ConsumerState<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends ConsumerState<MainLayout> {
  bool _isDrawerOpen = false;

  @override
  Widget build(BuildContext context) {
    final optionsNotifier = ref.read(optionsProvider.notifier);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final drawerWidth = isSmallScreen ? screenWidth * 0.7 : 250.0;

    return Scaffold(
      appBar: AppBar(
        title: _getTitleForRoute(widget.currentRoute),
        leading: IconButton(
          icon: const Icon(Icons.menu),
          onPressed: () {
            setState(() {
              _isDrawerOpen = !_isDrawerOpen;
            });
          },
        ),
        actions: [
          // Only show refresh button on data screens
          if (_isDataScreen(widget.currentRoute))
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                optionsNotifier.refresh();
              },
              tooltip: 'Refresh Data',
            ),

          // Settings button for all screens
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              context.go('/settings');
            },
            tooltip: 'Settings',
          ),

          // Profile button for all screens
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {
              context.go('/profile');
            },
            tooltip: 'Profile',
          ),
        ],
      ),
      // For mobile, add a bottom navigation bar for quicker access
      bottomNavigationBar: isSmallScreen ? _buildBottomNavBar(context) : null,
      body: Stack(
        children: [
          // Main content
          Positioned.fill(child: widget.child),

          // Overlay when drawer is open (for tap-to-close behavior)
          if (_isDrawerOpen)
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _isDrawerOpen = false;
                  });
                },
                child: Container(color: Colors.black54),
              ),
            ),

          // Navigation drawer - animated to slide in/out
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300),
            left: _isDrawerOpen ? 0 : -drawerWidth,
            top: 0,
            bottom: 0,
            width: drawerWidth,
            child: Material(elevation: 16, child: _buildNavigationDrawer(context)),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationDrawer(BuildContext context) {
    return Column(
      children: [
        DrawerHeader(
          padding: EdgeInsets.zero,
          margin: EdgeInsets.zero,
          decoration: BoxDecoration(color: Theme.of(context).primaryColor),
          child: Stack(
            children: [
              const Center(
                child: Text('Options AI', style: TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold)),
              ),
              // Close drawer button
              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () {
                    setState(() {
                      _isDrawerOpen = false;
                    });
                  },
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView(
            padding: EdgeInsets.zero,
            children: [
              _buildNavItem(context, 'Home', Icons.home, '/home', widget.currentRoute == '/home'),
              const Divider(),
              _buildNavItem(context, 'Option Chain', Icons.table_chart, '/option-chain', widget.currentRoute == '/option-chain'),
              _buildNavItem(context, 'IV Analysis', Icons.show_chart, '/iv-analysis', widget.currentRoute == '/iv-analysis'),
              _buildNavItem(context, 'OI Analysis', Icons.analytics, '/oi-analysis', widget.currentRoute == '/oi-analysis'),
              _buildNavItem(context, 'AI Agent', Icons.smart_toy, '/ai-agent', widget.currentRoute == '/ai-agent'),
              const Divider(),
              _buildNavItem(context, 'Settings', Icons.settings, '/settings', widget.currentRoute == '/settings'),
              _buildNavItem(context, 'Profile', Icons.person, '/profile', widget.currentRoute == '/profile'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNavItem(BuildContext context, String title, IconData icon, String route, bool isSelected) {
    return ListTile(
      leading: Icon(icon, color: isSelected ? Theme.of(context).primaryColor : null),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? Theme.of(context).primaryColor : null,
          fontWeight: isSelected ? FontWeight.bold : null,
        ),
      ),
      selected: isSelected,
      onTap: () {
        context.go(route);
        setState(() {
          _isDrawerOpen = false;
        });
      },
    );
  }

  // Bottom navigation bar for mobile screens
  Widget _buildBottomNavBar(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: _getNavIndex(widget.currentRoute),
      onTap: (index) {
        switch (index) {
          case 0:
            context.go('/home');
            break;
          case 1:
            context.go('/option-chain');
            break;
          case 2:
            context.go('/iv-analysis');
            break;
          case 3:
            context.go('/ai-agent');
            break;
        }
      },
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
        BottomNavigationBarItem(icon: Icon(Icons.table_chart), label: 'Chain'),
        BottomNavigationBarItem(icon: Icon(Icons.show_chart), label: 'IV'),
        BottomNavigationBarItem(icon: Icon(Icons.smart_toy), label: 'AI'),
      ],
    );
  }

  // Get the navigation index for the current route
  int _getNavIndex(String route) {
    switch (route) {
      case '/home':
        return 0;
      case '/option-chain':
        return 1;
      case '/iv-analysis':
        return 2;
      case '/ai-agent':
        return 3;
      default:
        return 0;
    }
  }

  // Check if the current screen is a data screen
  bool _isDataScreen(String route) {
    return ['/option-chain', '/iv-analysis', '/oi-analysis'].contains(route);
  }

  Widget _getTitleForRoute(String route) {
    switch (route) {
      case '/home':
        return const Text('Options AI');
      case '/option-chain':
        return const Text('Option Chain');
      case '/iv-analysis':
        return const Text('IV Analysis');
      case '/oi-analysis':
        return const Text('OI Analysis');
      case '/ai-agent':
        return const Text('AI Agent');
      case '/settings':
        return const Text('Settings');
      case '/profile':
        return const Text('Profile');
      default:
        return const Text('Options AI');
    }
  }
}
