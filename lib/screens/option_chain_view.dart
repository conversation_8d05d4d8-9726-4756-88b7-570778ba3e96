import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../providers/riverpod/options_provider.dart';
import '../widgets/option_chain_table.dart';
import '../widgets/option_chain_mobile_view.dart';

class OptionChainView extends ConsumerWidget {
  const OptionChainView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final optionsState = ref.watch(optionsProvider);
    final optionsNotifier = ref.read(optionsProvider.notifier);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    // Handle error case only - loading is handled implicitly
    if (optionsState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SelectableText(optionsState.error!),
            const SizedBox(height: 16),
            ElevatedButton(onPressed: () => optionsNotifier.refresh(), child: const Text('Retry')),
          ],
        ),
      );
    }

    // Show content regardless of loading state (for smoother updates)
    return Column(
      children: [
        // Use the mobile view for small screens and the table for larger screens
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: isSmallScreen ? const OptionChainMobileView() : const OptionChainTable(),
          ),
        ),
      ],
    );
  }
}
