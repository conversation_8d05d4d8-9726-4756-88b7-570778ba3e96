import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/riverpod/user_provider.dart';
import '../widgets/common/common_widgets.dart' as custom;

class EnhancedOnboardingScreen extends HookConsumerWidget {
  const EnhancedOnboardingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pageController = usePageController();
    final currentPageIndex = useState(0);
    final animationController = useAnimationController(duration: const Duration(milliseconds: 300));

    // User data collection
    final fullNameController = useTextEditingController();
    final phoneController = useTextEditingController();
    final selectedMarkets = useState<Set<String>>({'US'});
    final riskTolerance = useState<double>(0.5);
    final enableNotifications = useState(true);
    final enableEmailUpdates = useState(true);
    final tradingExperience = useState<String>('beginner');

    final theme = Theme.of(context);

    final onboardingSteps = [
      OnboardingStepData(
        title: 'Welcome to Options AI',
        subtitle: 'Your AI-Powered Trading Companion',
        description: 'Get intelligent insights, real-time analytics, and personalized trading recommendations.',
        icon: Icons.waving_hand,
        color: theme.colorScheme.primary,
        content: _buildWelcomeContent(context, theme),
      ),
      OnboardingStepData(
        title: 'Tell Us About Yourself',
        subtitle: 'Personalize Your Experience',
        description: 'Help us customize your trading dashboard and recommendations.',
        icon: Icons.person_outline,
        color: theme.colorScheme.secondary,
        content: _buildProfileContent(context, theme, fullNameController, phoneController, tradingExperience),
      ),
      OnboardingStepData(
        title: 'Choose Your Markets',
        subtitle: 'Select Your Trading Focus',
        description: 'Pick the markets and instruments you want to trade.',
        icon: Icons.trending_up,
        color: theme.colorScheme.tertiary,
        content: _buildMarketSelectionContent(context, theme, selectedMarkets),
      ),
      OnboardingStepData(
        title: 'Set Your Preferences',
        subtitle: 'Configure Your Settings',
        description: 'Customize your notifications and communication preferences.',
        icon: Icons.settings_outlined,
        color: Colors.purple,
        content: _buildPreferencesContent(context, theme, enableNotifications, enableEmailUpdates),
      ),
      OnboardingStepData(
        title: 'AI Assistant Setup',
        subtitle: 'Configure Your AI Helper',
        description: 'Set your risk tolerance and trading style preferences.',
        icon: Icons.psychology_outlined,
        color: Colors.indigo,
        content: _buildAISetupContent(context, theme, riskTolerance),
      ),
      OnboardingStepData(
        title: 'You\'re All Set!',
        subtitle: 'Ready to Start Trading',
        description: 'Your personalized Options AI experience is ready to go.',
        icon: Icons.rocket_launch_outlined,
        color: Colors.green,
        content: _buildCompletionContent(context, theme),
      ),
    ];

    void nextStep() {
      if (currentPageIndex.value < onboardingSteps.length - 1) {
        currentPageIndex.value++;
        pageController.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
        animationController.forward();
      } else {
        _completeOnboarding(ref, context);
      }
    }

    void previousStep() {
      if (currentPageIndex.value > 0) {
        currentPageIndex.value--;
        pageController.previousPage(duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
        animationController.reverse();
      }
    }

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withOpacity(0.1),
              theme.colorScheme.secondary.withOpacity(0.05),
              theme.colorScheme.background,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Enhanced Progress Header
              _buildProgressHeader(
                context,
                theme,
                currentPageIndex.value,
                onboardingSteps.length,
                () => _showSkipDialog(context, ref),
              ),

              // Main Content
              Expanded(
                child: PageView.builder(
                  controller: pageController,
                  onPageChanged: (index) {
                    currentPageIndex.value = index;
                    if (index > currentPageIndex.value) {
                      animationController.forward();
                    } else {
                      animationController.reverse();
                    }
                  },
                  itemCount: onboardingSteps.length,
                  itemBuilder: (context, index) {
                    final step = onboardingSteps[index];
                    return _buildOnboardingPage(context, theme, step, index, onboardingSteps.length, nextStep, previousStep);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressHeader(BuildContext context, ThemeData theme, int currentIndex, int totalSteps, VoidCallback onSkip) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Step ${currentIndex + 1} of $totalSteps',
                style: GoogleFonts.poppins(fontSize: 16, fontWeight: FontWeight.w600, color: theme.colorScheme.primary),
              ),
              if (currentIndex < totalSteps - 1)
                TextButton(
                  onPressed: onSkip,
                  style: TextButton.styleFrom(foregroundColor: theme.colorScheme.onSurfaceVariant),
                  child: const Text('Skip'),
                ),
            ],
          ),
          const SizedBox(height: 16),

          // Enhanced Progress Bar
          Container(
            height: 8,
            decoration: BoxDecoration(color: theme.colorScheme.surfaceVariant, borderRadius: BorderRadius.circular(4)),
            child: Row(
              children: List.generate(totalSteps, (index) {
                return Expanded(
                  child: Container(
                    margin: EdgeInsets.only(right: index < totalSteps - 1 ? 4 : 0),
                    decoration: BoxDecoration(
                      color: index <= currentIndex ? theme.colorScheme.primary : Colors.transparent,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOnboardingPage(
    BuildContext context,
    ThemeData theme,
    OnboardingStepData step,
    int index,
    int totalSteps,
    VoidCallback onNext,
    VoidCallback onPrevious,
  ) {
    final isFirst = index == 0;
    final isLast = index == totalSteps - 1;

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // Animated Icon
          FadeInDown(
            duration: const Duration(milliseconds: 600),
            delay: Duration(milliseconds: index * 100),
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: step.color.withOpacity(0.1),
                shape: BoxShape.circle,
                border: Border.all(color: step.color.withOpacity(0.3), width: 2),
              ),
              child: Icon(step.icon, size: 60, color: step.color),
            ),
          ),

          const SizedBox(height: 32),

          // Title and Subtitle
          FadeInUp(
            duration: const Duration(milliseconds: 600),
            delay: Duration(milliseconds: 200 + index * 100),
            child: Column(
              children: [
                Text(
                  step.title,
                  style: GoogleFonts.poppins(fontSize: 28, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  step.subtitle,
                  style: theme.textTheme.titleMedium?.copyWith(color: step.color, fontWeight: FontWeight.w600),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  step.description,
                  style: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 40),

          // Step Content
          Expanded(
            child: FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: Duration(milliseconds: 400 + index * 100),
              child: step.content,
            ),
          ),

          // Navigation Buttons
          FadeInUp(
            duration: const Duration(milliseconds: 600),
            delay: Duration(milliseconds: 600 + index * 100),
            child: _buildNavigationButtons(theme, isFirst, isLast, onPrevious, onNext),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons(ThemeData theme, bool isFirst, bool isLast, VoidCallback onPrevious, VoidCallback onNext) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (!isFirst)
            custom.AnimatedButton(
              text: 'Previous',
              onPressed: onPrevious,
              backgroundColor: Colors.transparent,
              textColor: theme.colorScheme.onSurfaceVariant,
              borderRadius: 12,
              icon: Icons.arrow_back_ios,
              width: 120,
              height: 48,
            )
          else
            const SizedBox(width: 120),

          custom.AnimatedButton(
            text: isLast ? 'Get Started' : 'Continue',
            onPressed: onNext,
            backgroundColor: theme.colorScheme.primary,
            textColor: Colors.white,
            borderRadius: 12,
            icon: isLast ? Icons.rocket_launch : Icons.arrow_forward_ios,
            width: 160,
            height: 56,
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeContent(BuildContext context, ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(color: theme.colorScheme.primary.withOpacity(0.1), blurRadius: 20, offset: const Offset(0, 10)),
            ],
          ),
          child: Column(
            children: [
              Icon(Icons.analytics, size: 60, color: theme.colorScheme.primary),
              const SizedBox(height: 24),
              Text(
                'Advanced Analytics',
                style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'Get professional-grade options analytics powered by AI.',
                style: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfileContent(
    BuildContext context,
    ThemeData theme,
    TextEditingController nameController,
    TextEditingController phoneController,
    ValueNotifier<String> experience,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
          ),
          child: Column(
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: 'Full Name',
                  prefixIcon: const Icon(Icons.person_outline),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                controller: phoneController,
                decoration: InputDecoration(
                  labelText: 'Phone Number (Optional)',
                  prefixIcon: const Icon(Icons.phone_outlined),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 24),

              // Trading Experience Selector
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Trading Experience', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
                  const SizedBox(height: 12),
                  ...['beginner', 'intermediate', 'advanced'].map((level) {
                    return RadioListTile<String>(
                      title: Text(level.toUpperCase()),
                      value: level,
                      groupValue: experience.value,
                      onChanged: (value) => experience.value = value!,
                      contentPadding: EdgeInsets.zero,
                    );
                  }),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMarketSelectionContent(BuildContext context, ThemeData theme, ValueNotifier<Set<String>> selectedMarkets) {
    final markets = [
      {'name': 'US Stocks', 'code': 'US', 'icon': Icons.flag},
      {'name': 'European Markets', 'code': 'EU', 'icon': Icons.euro},
      {'name': 'Asian Markets', 'code': 'ASIA', 'icon': Icons.language},
      {'name': 'Cryptocurrency', 'code': 'CRYPTO', 'icon': Icons.currency_bitcoin},
      {'name': 'Commodities', 'code': 'COMMODITIES', 'icon': Icons.agriculture},
      {'name': 'Forex', 'code': 'FOREX', 'icon': Icons.currency_exchange},
    ];

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select markets you\'re interested in:',
                style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 20),

              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 2.5,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: markets.length,
                itemBuilder: (context, index) {
                  final market = markets[index];
                  final isSelected = selectedMarkets.value.contains(market['code']);

                  return GestureDetector(
                    onTap: () {
                      final newSelection = Set<String>.from(selectedMarkets.value);
                      if (isSelected) {
                        newSelection.remove(market['code']);
                      } else {
                        newSelection.add(market['code'] as String);
                      }
                      selectedMarkets.value = newSelection;
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? theme.colorScheme.primary.withOpacity(0.1)
                            : theme.colorScheme.surfaceVariant.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected ? theme.colorScheme.primary : theme.colorScheme.outline.withOpacity(0.3),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            market['icon'] as IconData,
                            size: 20,
                            color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              market['name'] as String,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPreferencesContent(
    BuildContext context,
    ThemeData theme,
    ValueNotifier<bool> notifications,
    ValueNotifier<bool> emailUpdates,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
          ),
          child: Column(
            children: [
              SwitchListTile(
                title: const Text('Push Notifications'),
                subtitle: const Text('Get real-time trading alerts and market updates'),
                value: notifications.value,
                onChanged: (value) => notifications.value = value,
                contentPadding: EdgeInsets.zero,
              ),
              const Divider(),
              SwitchListTile(
                title: const Text('Email Updates'),
                subtitle: const Text('Receive weekly market insights and analysis'),
                value: emailUpdates.value,
                onChanged: (value) => emailUpdates.value = value,
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAISetupContent(BuildContext context, ThemeData theme, ValueNotifier<double> riskTolerance) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
          ),
          child: Column(
            children: [
              Text(
                'Configure your AI assistant preferences:',
                style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              Text(
                'Risk Tolerance: ${(riskTolerance.value * 100).toInt()}%',
                style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: theme.colorScheme.primary),
              ),
              const SizedBox(height: 16),

              Slider(
                value: riskTolerance.value,
                onChanged: (value) => riskTolerance.value = value,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                activeColor: theme.colorScheme.primary,
              ),
              const SizedBox(height: 16),

              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  riskTolerance.value < 0.3
                      ? '🛡️ Conservative - Lower risk, stable returns'
                      : riskTolerance.value < 0.7
                      ? '⚖️ Balanced - Moderate risk and returns'
                      : '🚀 Aggressive - Higher risk, potential for higher returns',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCompletionContent(BuildContext context, ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [theme.colorScheme.primary.withOpacity(0.1), theme.colorScheme.secondary.withOpacity(0.1)],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: theme.colorScheme.primary.withOpacity(0.3), width: 2),
          ),
          child: Column(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(color: Colors.green.withOpacity(0.2), shape: BoxShape.circle),
                child: const Icon(Icons.check_circle_outline, size: 50, color: Colors.green),
              ),
              const SizedBox(height: 24),
              Text(
                'Welcome to Options AI!',
                style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold, color: theme.colorScheme.primary),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Your personalized trading dashboard is ready. Start exploring advanced options analytics and AI-powered insights.',
                style: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _showSkipDialog(BuildContext context, WidgetRef ref) async {
    final shouldSkip = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Skip Onboarding?'),
        content: const Text(
          'You can always complete your profile setup later in settings. '
          'However, we recommend completing the setup for a better experience.',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('Continue Setup')),
          TextButton(onPressed: () => Navigator.of(context).pop(true), child: const Text('Skip')),
        ],
      ),
    );

    if (shouldSkip == true) {
      _completeOnboarding(ref, context);
    }
  }

  Future<void> _completeOnboarding(WidgetRef ref, BuildContext context) async {
    try {
      await ref.read(userProfileNotifierProvider.notifier).completeOnboarding();
      if (context.mounted) {
        context.go('/home');
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error completing onboarding: $e'), backgroundColor: Theme.of(context).colorScheme.error),
        );
      }
    }
  }
}

class OnboardingStepData {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final Color color;
  final Widget content;

  const OnboardingStepData({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.color,
    required this.content,
  });
}
