import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:animate_do/animate_do.dart';
import '../widgets/home_views/home_overview_view.dart';
import '../widgets/home_views/news_view.dart';
import '../widgets/home_views/trending_view.dart';
import '../widgets/home_views/ai_assistants_view.dart';
import '../widgets/home_views/settings_view.dart';
import '../widgets/common/common_widgets.dart' as custom;

class NewHomeScreen extends ConsumerStatefulWidget {
  const NewHomeScreen({super.key});

  @override
  ConsumerState<NewHomeScreen> createState() => _NewHomeScreenState();
}

class _NewHomeScreenState extends ConsumerState<NewHomeScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final List<HomeTab> _tabs = [
    HomeTab(icon: Icons.home, label: 'Home', view: const HomeOverviewView()),
    HomeTab(icon: Icons.newspaper, label: 'News', view: const NewsView()),
    HomeTab(icon: Icons.trending_up, label: 'Trending', view: const TrendingView()),
    HomeTab(icon: Icons.psychology, label: 'AI Assistants', view: const AIAssistantsView()),
    HomeTab(icon: Icons.settings, label: 'Settings', view: const SettingsView()),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _refreshData() {
    // Add haptic feedback
    // HapticFeedback.mediumImpact(); // Uncomment if you want haptic feedback
    
    // Trigger refresh for current tab content
    // This could refresh market data, user portfolio, etc.
    setState(() {
      // Force rebuild with fresh data
    });
    
    // Show brief success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 20),
            SizedBox(width: 8),
            Text('Data refreshed successfully'),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.background,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 120,
            floating: false,
            pinned: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            flexibleSpace: FlexibleSpaceBar(
              title: FadeInDown(
                duration: const Duration(milliseconds: 500),
                child: Text(
                  'Options AI',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      colorScheme.primary.withOpacity(0.15),
                      colorScheme.secondary.withOpacity(0.08),
                      colorScheme.tertiary.withOpacity(0.05),
                    ],
                    stops: const [0.0, 0.6, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    // Animated background pattern
                    Positioned(
                      top: -20,
                      right: -20,
                      child: FadeIn(
                        duration: const Duration(milliseconds: 2000),
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withOpacity(0.05),
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: -30,
                      left: -30,
                      child: FadeIn(
                        duration: const Duration(milliseconds: 2500),
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withOpacity(0.03),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(60),
              child: FadeInUp(
                duration: const Duration(milliseconds: 600),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    children: _tabs.asMap().entries.map((entry) {
                      final index = entry.key;
                      final tab = entry.value;
                      final isSelected = _tabController.index == index;
                      
                      return Expanded(
                        child: GestureDetector(
                          onTap: () {
                            _tabController.animateTo(index);
                            setState(() {});
                          },
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            decoration: BoxDecoration(
                              color: isSelected 
                                  ? colorScheme.primary.withOpacity(0.1)
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(20),
                              border: isSelected 
                                  ? Border.all(color: colorScheme.primary.withOpacity(0.3))
                                  : null,
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  tab.icon,
                                  size: 20,
                                  color: isSelected 
                                      ? colorScheme.primary 
                                      : colorScheme.onSurfaceVariant,
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  tab.label,
                                  style: theme.textTheme.labelSmall?.copyWith(
                                    color: isSelected 
                                        ? colorScheme.primary 
                                        : colorScheme.onSurfaceVariant,
                                    fontSize: 10,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ),
          SliverFillRemaining(
            child: custom.AnimatedPageWrapper(
              child: TabBarView(
                controller: _tabController,
                children: _tabs.map((tab) => tab.view).toList(),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FadeInUp(
        duration: const Duration(milliseconds: 800),
        child: FloatingActionButton.extended(
          onPressed: () {
            // Add refresh or quick action with haptic feedback
            _refreshData();
          },
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          elevation: 8,
          icon: const Icon(Icons.refresh),
          label: const Text('Refresh'),
          heroTag: 'refresh_fab',
        ),
      ),
    );
  }
}

class HomeTab {
  final IconData icon;
  final String label;
  final Widget view;

  HomeTab({required this.icon, required this.label, required this.view});
}
