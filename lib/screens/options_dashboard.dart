import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../providers/riverpod/options_provider.dart';
import '../widgets/option_chain_table.dart';

class OptionsDashboard extends ConsumerWidget {
  const OptionsDashboard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final optionsNotifier = ref.read(optionsProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Options Chain'),
        actions: [IconButton(icon: const Icon(Icons.refresh), onPressed: () => optionsNotifier.refresh())],
      ),
      body: const Padding(padding: EdgeInsets.all(8.0), child: OptionChainTable()),
    );
  }
}
