import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/user_profile_simple.dart';
import '../providers/riverpod/user_provider.dart';
import '../widgets/onboarding_step_widget.dart';

class OnboardingScreen extends HookConsumerWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pageController = usePageController();
    final currentPageIndex = useState(0);
    final steps = ref.watch(onboardingStepsNotifierProvider);
    final theme = Theme.of(context);

    void nextStep() {
      if (currentPageIndex.value < steps.length - 1) {
        currentPageIndex.value++;
        pageController.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
      } else {
        // Complete onboarding
        _completeOnboarding(ref, context);
      }
    }

    void previousStep() {
      if (currentPageIndex.value > 0) {
        currentPageIndex.value--;
        pageController.previousPage(duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
      }
    }

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Step ${currentPageIndex.value + 1} of ${steps.length}',
                        style: theme.textTheme.labelLarge?.copyWith(color: theme.colorScheme.primary),
                      ),
                      if (currentPageIndex.value > 0)
                        TextButton(onPressed: () => _skipOnboarding(ref, context), child: Text('Skip')),
                    ],
                  ),
                  const SizedBox(height: 10),
                  LinearProgressIndicator(
                    value: (currentPageIndex.value + 1) / steps.length,
                    backgroundColor: theme.colorScheme.surfaceVariant,
                    valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                  ),
                ],
              ),
            ),

            // Onboarding content
            Expanded(
              child: PageView.builder(
                controller: pageController,
                onPageChanged: (index) => currentPageIndex.value = index,
                itemCount: steps.length,
                itemBuilder: (context, index) {
                  final step = steps[index];
                  return OnboardingStepWidget(
                    step: step,
                    isFirst: index == 0,
                    isLast: index == steps.length - 1,
                    onNext: nextStep,
                    onPrevious: previousStep,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _completeOnboarding(WidgetRef ref, BuildContext context) async {
    try {
      await ref.read(userProfileNotifierProvider.notifier).completeOnboarding();
      if (context.mounted) {
        context.go('/home');
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error completing onboarding: $e'), backgroundColor: Theme.of(context).colorScheme.error),
        );
      }
    }
  }

  Future<void> _skipOnboarding(WidgetRef ref, BuildContext context) async {
    final shouldSkip = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Skip Onboarding?'),
            content: const Text('You can always complete your profile setup later in settings.'),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('Cancel')),
              TextButton(onPressed: () => Navigator.of(context).pop(true), child: const Text('Skip')),
            ],
          ),
    );

    if (shouldSkip == true) {
      _completeOnboarding(ref, context);
    }
  }
}
