import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logarte/logarte.dart';
import '../providers/riverpod/options_provider.dart';
import '../config/logarte_config.dart';

// Provider for theme mode
final themeModeProvider = StateProvider<ThemeMode>((ref) => ThemeMode.dark);

// Provider for notification settings
final notificationsEnabledProvider = StateProvider<bool>((ref) => true);

// Provider for auto refresh interval (in seconds)
final refreshIntervalProvider = StateProvider<int>((ref) => 30);

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);
    final notificationsEnabled = ref.watch(notificationsEnabledProvider);
    final refreshInterval = ref.watch(refreshIntervalProvider);
    final autoRefreshEnabled = ref.watch(optionsProvider).autoRefreshEnabled;

    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: isSmallScreen ? double.infinity : 600),
          child: Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Settings', style: Theme.of(context).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold)),
                const SizedBox(height: 24),

                // Theme Section
                _buildSectionTitle(context, 'App Theme'),
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        _buildThemeOption(context, 'Light Theme', ThemeMode.light, themeMode, ref),
                        const Divider(),
                        _buildThemeOption(context, 'Dark Theme', ThemeMode.dark, themeMode, ref),
                        const Divider(),
                        _buildThemeOption(context, 'System Theme', ThemeMode.system, themeMode, ref),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Data Refresh Section
                _buildSectionTitle(context, 'Data Settings'),
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        SwitchListTile(
                          title: const Text('Auto-Refresh Data'),
                          subtitle: const Text('Automatically update option chain data'),
                          value: autoRefreshEnabled,
                          onChanged: (value) {
                            ref.read(optionsProvider.notifier).setAutoRefresh(value);
                          },
                        ),
                        const Divider(),
                        ListTile(
                          title: const Text('Refresh Interval'),
                          subtitle: Text('$refreshInterval seconds'),
                          enabled: autoRefreshEnabled,
                          trailing: DropdownButton<int>(
                            value: refreshInterval,
                            onChanged:
                                autoRefreshEnabled
                                    ? (value) {
                                      if (value != null) {
                                        ref.read(refreshIntervalProvider.notifier).state = value;
                                        // Set refresh interval in seconds - the provider will convert to Duration
                                        ref.read(optionsProvider.notifier).setRefreshInterval(Duration(seconds: value));
                                      }
                                    }
                                    : null,
                            items: const [
                              DropdownMenuItem(value: 10, child: Text('10 seconds')),
                              DropdownMenuItem(value: 30, child: Text('30 seconds')),
                              DropdownMenuItem(value: 60, child: Text('1 minute')),
                              DropdownMenuItem(value: 300, child: Text('5 minutes')),
                            ],
                          ),
                        ),
                        const Divider(),
                        ListTile(
                          title: const Text('Clear Cache'),
                          subtitle: const Text('Remove locally stored data'),
                          trailing: ElevatedButton(
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder:
                                    (context) => AlertDialog(
                                      title: const Text('Clear Cache'),
                                      content: const Text(
                                        'This will clear all locally stored data. You will need to download everything again. Continue?',
                                      ),
                                      actions: [
                                        TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
                                        TextButton(
                                          onPressed: () {
                                            // Reset the provider state instead of using clearCache()
                                            ref.read(optionsProvider.notifier).refresh();
                                            Navigator.pop(context);

                                            ScaffoldMessenger.of(
                                              context,
                                            ).showSnackBar(const SnackBar(content: Text('Data refreshed successfully')));
                                          },
                                          child: const Text('Clear'),
                                        ),
                                      ],
                                    ),
                              );
                            },
                            child: const Text('Clear'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Notifications Section
                _buildSectionTitle(context, 'Notifications'),
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        SwitchListTile(
                          title: const Text('Enable Notifications'),
                          subtitle: const Text('Receive important market alerts'),
                          value: notificationsEnabled,
                          onChanged: (value) {
                            ref.read(notificationsEnabledProvider.notifier).state = value;
                          },
                        ),
                        const Divider(),
                        SwitchListTile(
                          title: const Text('Price Alerts'),
                          subtitle: const Text('Notify when price reaches targets'),
                          value: notificationsEnabled,
                          onChanged:
                              notificationsEnabled
                                  ? (value) {
                                    // In a real app, you would store this in a specific provider
                                  }
                                  : null,
                        ),
                        const Divider(),
                        SwitchListTile(
                          title: const Text('Volatility Alerts'),
                          subtitle: const Text('Notify on significant IV changes'),
                          value: notificationsEnabled,
                          onChanged:
                              notificationsEnabled
                                  ? (value) {
                                    // In a real app, you would store this in a specific provider
                                  }
                                  : null,
                        ),
                        const Divider(),
                        SwitchListTile(
                          title: const Text('OI Change Alerts'),
                          subtitle: const Text('Notify on large open interest changes'),
                          value: notificationsEnabled,
                          onChanged:
                              notificationsEnabled
                                  ? (value) {
                                    // In a real app, you would store this in a specific provider
                                  }
                                  : null,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // About Section
                _buildSectionTitle(context, 'About'),
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        // Add hidden debug trigger to version
                        LogarteMagicalTap(
                          logarte: logarte,
                          child: ListTile(title: const Text('Version'), subtitle: const Text('1.0.0')),
                        ),
                        const Divider(),
                        ListTile(
                          title: const Text('Terms of Service'),
                          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                          onTap: () {
                            // Navigate to Terms of Service
                          },
                        ),
                        const Divider(),
                        ListTile(
                          title: const Text('Privacy Policy'),
                          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                          onTap: () {
                            // Navigate to Privacy Policy
                          },
                        ),
                        const Divider(),
                        ListTile(
                          title: const Text('Contact Support'),
                          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                          onTap: () {
                            // Open support contact options
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0, left: 4.0),
      child: Text(title, style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildThemeOption(BuildContext context, String title, ThemeMode mode, ThemeMode currentMode, WidgetRef ref) {
    return RadioListTile<ThemeMode>(
      title: Text(title),
      value: mode,
      groupValue: currentMode,
      onChanged: (ThemeMode? value) {
        if (value != null) {
          ref.read(themeModeProvider.notifier).state = value;
        }
      },
    );
  }
}
