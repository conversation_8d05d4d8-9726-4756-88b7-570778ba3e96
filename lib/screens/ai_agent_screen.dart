import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import '../providers/riverpod/options_provider.dart';

class AIAgentScreen extends ConsumerStatefulWidget {
  const AIAgentScreen({super.key});

  @override
  ConsumerState<AIAgentScreen> createState() => _AIAgentScreenState();
}

class _AIAgentScreenState extends ConsumerState<AIAgentScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  bool _isTyping = false;

  @override
  void initState() {
    super.initState();
    // Add initial greeting message
    _messages.add(
      ChatMessage(
        text:
            "Hello! I'm your Options AI assistant. I can help you analyze market trends, interpret option chains, and provide trading insights. What would you like to know today?",
        isUserMessage: false,
        timestamp: DateTime.now(),
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    final userMessage = _messageController.text;
    setState(() {
      _messages.add(ChatMessage(text: userMessage, isUserMessage: true, timestamp: DateTime.now()));
      _messageController.clear();
      _isTyping = true;
    });

    // Scroll to bottom after adding message
    _scrollToBottom();

    // Simulate AI response (replace with actual API call in production)
    _generateAIResponse(userMessage);
  }

  void _generateAIResponse(String userMessage) {
    // Get options data for context
    final optionsState = ref.read(optionsProvider);
    final optionData = optionsState.optionChain.isNotEmpty ? optionsState.optionChain.first : null;

    String spotPrice = optionData != null ? '\$${optionData.spotPrice.toStringAsFixed(2)}' : 'unavailable';

    // Simulate AI thinking time
    Future.delayed(const Duration(seconds: 1), () {
      String aiResponse;

      // Simple pattern matching for demo purposes - replace with actual AI in production
      if (userMessage.toLowerCase().contains('market') || userMessage.toLowerCase().contains('trend')) {
        aiResponse =
            "Based on current option chain data, the market seems to be showing a bullish trend with higher call volumes. The current spot price is $spotPrice.";
      } else if (userMessage.toLowerCase().contains('volatility') || userMessage.toLowerCase().contains('iv')) {
        aiResponse =
            "The implied volatility is currently elevated, indicating market uncertainty. IV skew suggests traders are pricing in potential downside risk.";
      } else if (userMessage.toLowerCase().contains('strategy') || userMessage.toLowerCase().contains('trade')) {
        aiResponse =
            "Given the current market conditions, you might consider a bull put spread strategy to capitalize on the upward momentum while limiting risk. Always ensure this aligns with your risk tolerance.";
      } else {
        aiResponse =
            "I'd be happy to help with that. The current market price is $spotPrice. To provide more specific insights, could you tell me which aspect of options trading you're interested in?";
      }

      setState(() {
        _messages.add(ChatMessage(text: aiResponse, isUserMessage: false, timestamp: DateTime.now()));
        _isTyping = false;
      });

      // Scroll to bottom after response
      _scrollToBottom();
    });
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      body: Column(
        children: [
          // Market Insights Card
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'AI Market Insights',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Icon(Icons.auto_awesome, color: Theme.of(context).colorScheme.primary),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'The market shows increased call option activity around key resistance levels. Implied volatility is trending lower, suggesting potential price consolidation.',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Chip(
                          label: const Text('Bullish Sentiment'),
                          backgroundColor: Colors.green.withOpacity(0.2),
                          labelStyle: const TextStyle(color: Colors.green, fontSize: 12),
                        ),
                        const SizedBox(width: 8),
                        Chip(
                          label: const Text('IV Decreasing'),
                          backgroundColor: Colors.blue.withOpacity(0.2),
                          labelStyle: const TextStyle(color: Colors.blue, fontSize: 12),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Chat Messages
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                return ChatBubble(message: message, isSmallScreen: isSmallScreen);
              },
            ),
          ),

          // "AI is typing" indicator
          if (_isTyping)
            Padding(
              padding: const EdgeInsets.only(left: 24.0, bottom: 8.0),
              child: Row(
                children: [
                  Text(
                    'AI is thinking...',
                    style: TextStyle(fontSize: 12, color: Theme.of(context).hintColor, fontStyle: FontStyle.italic),
                  ),
                  const SizedBox(width: 8),
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                    ),
                  ),
                ],
              ),
            ),

          // Message Input
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Ask about market trends, strategies...',
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(24.0)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                    ),
                    onSubmitted: (_) => _sendMessage(),
                    maxLines: 1,
                  ),
                ),
                const SizedBox(width: 8.0),
                FloatingActionButton(onPressed: _sendMessage, child: const Icon(Icons.send), mini: true),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ChatMessage {
  final String text;
  final bool isUserMessage;
  final DateTime timestamp;

  ChatMessage({required this.text, required this.isUserMessage, required this.timestamp});
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isSmallScreen;

  const ChatBubble({super.key, required this.message, required this.isSmallScreen});

  @override
  Widget build(BuildContext context) {
    final maxWidth = isSmallScreen ? MediaQuery.of(context).size.width * 0.7 : MediaQuery.of(context).size.width * 0.5;

    return Align(
      alignment: message.isUserMessage ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        constraints: BoxConstraints(maxWidth: maxWidth),
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        decoration: BoxDecoration(
          color: message.isUserMessage ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16.0),
          boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 4.0, offset: const Offset(0, 2))],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.text,
              style: TextStyle(
                color: message.isUserMessage ? Theme.of(context).colorScheme.onPrimary : Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 4.0),
            Text(
              DateFormat('h:mm a').format(message.timestamp),
              style: TextStyle(
                fontSize: 10.0,
                color:
                    message.isUserMessage
                        ? Theme.of(context).colorScheme.onPrimary.withOpacity(0.7)
                        : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
