import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../providers/riverpod/options_provider.dart';
import '../screens/iv_analysis_view.dart';
import '../screens/oi_analysis_view.dart';
import '../screens/option_chain_view.dart';

class AnalysisScreen extends ConsumerWidget {
  const AnalysisScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final optionsNotifier = ref.read(optionsProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Options Analysis'),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: () => optionsNotifier.refresh()),
          IconButton(icon: const Icon(Icons.person), onPressed: () => context.go('/profile')),
        ],
      ),
      body: DefaultTabController(
        length: 3,
        child: <PERSON>umn(
          children: [
            const TabBar(tabs: [Tab(text: 'Option Chain'), Tab(text: 'IV Analysis'), Tab(text: 'OI Analysis')]),
            Expanded(
              child: TabBarView(
                children: [
                  // Option Chain Tab
                  const OptionChainView(),

                  // IV Analysis Tab
                  const IVAnalysisView(),

                  // OI Analysis Tab
                  const OIAnalysisView(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
