import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../providers/riverpod/options_provider.dart';
import '../widgets/oi_analysis_chart.dart';

class OIAnalysisView extends ConsumerWidget {
  const OIAnalysisView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final optionsState = ref.watch(optionsProvider);
    final optionsNotifier = ref.read(optionsProvider.notifier);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Open Interest Analysis', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),

          // Expiry date selector and timestamp
          Row(
            children: [
              // Expiry date dropdown
              if (optionsState.expiryDates.isNotEmpty) ...[
                const Text('Expiry Date: '),
                DropdownButton<String>(
                  value: optionsState.selectedExpiryDate,
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      optionsNotifier.setSelectedExpiryDate(newValue);
                    }
                  },
                  items:
                      optionsState.expiryDates.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(value: value, child: Text(value));
                      }).toList(),
                ),
                const Spacer(),
                if (optionsState.lastTimestamp != null)
                  Text(
                    'Last updated: ${optionsState.lastTimestamp!.toLocal().toString().substring(0, 19)}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
              ],
            ],
          ),

          const SizedBox(height: 16),

          // Main content area - shows loading, error, or chart
          Expanded(
            child: Builder(
              builder: (context) {
                // Handle loading state
                if (optionsState.isLoading && optionsState.optionChain.isEmpty) {
                  return const Center(child: CircularProgressIndicator());
                }

                // Handle error state
                if (optionsState.error != null && optionsState.optionChain.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text('Error: ${optionsState.error}'),
                        const SizedBox(height: 16),
                        ElevatedButton(onPressed: () => optionsNotifier.refresh(), child: const Text('Retry')),
                      ],
                    ),
                  );
                }

                // Show chart with data
                if (optionsState.optionChain.isNotEmpty) {
                  return OIAnalysisChart(
                    data: optionsState.optionChain,
                    historicalData: optionsState.optionChain,
                    atmStrike: optionsNotifier.getAtmStrike(),
                  );
                }

                // Default empty state
                return const Center(child: Text('No data available'));
              },
            ),
          ),
        ],
      ),
    );
  }
}
