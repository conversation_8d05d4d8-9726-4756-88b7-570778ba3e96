import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:animate_do/animate_do.dart';
import 'dart:math' as math;
import '../providers/riverpod/auth_provider.dart';
import '../widgets/common/common_widgets.dart' as custom;

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final isLoading = useState(false);
    final isLogin = useState(true); // true for login, false for signup
    final obscurePassword = useState(true);
    final pageController = usePageController();

    // Get the authentication provider
    final authNotifier = ref.read(authProvider.notifier);

    Future<void> submitForm() async {
      if (!formKey.currentState!.validate()) return;

      isLoading.value = true;

      try {
        if (isLogin.value) {
          await authNotifier.signIn(email: emailController.text.trim(), password: passwordController.text);
        } else {
          await authNotifier.signUp(email: emailController.text.trim(), password: passwordController.text);
        }
        // Navigate to home screen
        context.go('/');
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e.toString()),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      } finally {
        isLoading.value = false;
      }
    }

    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Scaffold(
      body: Stack(
        children: [
          // Animated gradient background
          AnimatedContainer(
            duration: const Duration(seconds: 10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  theme.colorScheme.primary.withOpacity(0.8),
                  theme.colorScheme.secondary.withOpacity(0.6),
                  theme.colorScheme.tertiary.withOpacity(0.4),
                ],
                stops: const [0.0, 0.6, 1.0],
              ),
            ),
          ),
          
          // Floating circles for background decoration
          Positioned(
            top: -100,
            right: -100,
            child: FadeIn(
              duration: const Duration(milliseconds: 2000),
              child: Container(
                width: 300,
                height: 300,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
            ),
          ),
          Positioned(
            bottom: -150,
            left: -150,
            child: FadeIn(
              duration: const Duration(milliseconds: 2500),
              child: Container(
                width: 400,
                height: 400,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.05),
                ),
              ),
            ),
          ),
          
          // Main content
          SafeArea(
            child: Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: isSmallScreen ? double.infinity : 400,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Enhanced Logo with pulse and bounce animation
                      BounceInDown(
                        duration: const Duration(milliseconds: 1000),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            // Pulsing background effect
                            TweenAnimationBuilder<double>(
                              tween: Tween<double>(begin: 0.0, end: 1.0),
                              duration: const Duration(milliseconds: 2000),
                              builder: (context, value, child) {
                                return Transform.scale(
                                  scale: 1.0 + (0.1 * math.sin(value * math.pi * 4)),
                                  child: Container(
                                    width: 120,
                                    height: 120,
                                    decoration: BoxDecoration(
                                      gradient: RadialGradient(
                                        colors: [
                                          theme.colorScheme.primary.withOpacity(0.2),
                                          Colors.transparent,
                                        ],
                                        stops: const [0.0, 1.0],
                                      ),
                                      borderRadius: BorderRadius.circular(60),
                                    ),
                                  ),
                                );
                              },
                            ),
                            // Main logo container
                            Container(
                              width: 100,
                              height: 100,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withOpacity(0.95),
                                    Colors.white.withOpacity(0.85),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(30),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 20,
                                    offset: const Offset(0, 10),
                                  ),
                                  BoxShadow(
                                    color: theme.colorScheme.primary.withOpacity(0.3),
                                    blurRadius: 30,
                                    offset: const Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  // Animated trending line background
                                  TweenAnimationBuilder<double>(
                                    tween: Tween<double>(begin: 0.0, end: 1.0),
                                    duration: const Duration(milliseconds: 1500),
                                    builder: (context, value, child) {
                                      return CustomPaint(
                                        size: const Size(60, 40),
                                        painter: TrendingLinePainter(
                                          progress: value,
                                          color: theme.colorScheme.primary.withOpacity(0.3),
                                        ),
                                      );
                                    },
                                  ),
                                  // Main icon
                                  Icon(
                                    Icons.trending_up_rounded,
                                    size: 50,
                                    color: theme.colorScheme.primary,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 32),
                      
                      // App title with fade animation
                      FadeInUp(
                        duration: const Duration(milliseconds: 800),
                        delay: const Duration(milliseconds: 200),
                        child: Text(
                          'Options AI',
                          style: theme.textTheme.headlineLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 36,
                            shadows: [
                              Shadow(
                                color: Colors.black.withOpacity(0.3),
                                offset: const Offset(0, 2),
                                blurRadius: 4,
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Subtitle with fade animation
                      FadeInUp(
                        duration: const Duration(milliseconds: 800),
                        delay: const Duration(milliseconds: 400),
                        child: Text(
                          isLogin.value ? 'Welcome back!' : 'Join the future of trading',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 18,
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 48),
                      
                      // Glass morphism form card
                      FadeInUp(
                        duration: const Duration(milliseconds: 800),
                        delay: const Duration(milliseconds: 600),
                        child: custom.GlassCard(
                          padding: const EdgeInsets.all(32),
                          child: Form(
                            key: formKey,
                            child: Column(
                              children: [
                                // Email field with animation
                                SlideInLeft(
                                  duration: const Duration(milliseconds: 600),
                                  delay: const Duration(milliseconds: 800),
                                  child: _buildTextField(
                                    controller: emailController,
                                    label: 'Email',
                                    icon: Icons.email_outlined,
                                    keyboardType: TextInputType.emailAddress,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter your email';
                                      }
                                      if (!value.contains('@')) {
                                        return 'Please enter a valid email';
                                      }
                                      return null;
                                    },
                                    theme: theme,
                                  ),
                                ),
                                
                                const SizedBox(height: 24),
                                
                                // Password field with animation
                                SlideInRight(
                                  duration: const Duration(milliseconds: 600),
                                  delay: const Duration(milliseconds: 1000),
                                  child: _buildTextField(
                                    controller: passwordController,
                                    label: 'Password',
                                    icon: Icons.lock_outline,
                                    obscureText: obscurePassword.value,
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        obscurePassword.value 
                                            ? Icons.visibility_outlined 
                                            : Icons.visibility_off_outlined,
                                        color: Colors.white.withOpacity(0.7),
                                      ),
                                      onPressed: () => obscurePassword.value = !obscurePassword.value,
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter your password';
                                      }
                                      if (value.length < 6) {
                                        return 'Password must be at least 6 characters';
                                      }
                                      return null;
                                    },
                                    theme: theme,
                                  ),
                                ),
                                
                                const SizedBox(height: 16),
                                
                                // Forgot password (only for login)
                                if (isLogin.value)
                                  FadeIn(
                                    duration: const Duration(milliseconds: 500),
                                    delay: const Duration(milliseconds: 1200),
                                    child: Align(
                                      alignment: Alignment.centerRight,
                                      child: TextButton(
                                        onPressed: () {
                                          // Forgot password logic
                                        },
                                        child: Text(
                                          'Forgot Password?',
                                          style: TextStyle(
                                            color: Colors.white.withOpacity(0.8),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                
                                const SizedBox(height: 32),
                                
                                // Submit button with loading animation
                                ZoomIn(
                                  duration: const Duration(milliseconds: 600),
                                  delay: const Duration(milliseconds: 1400),
                                  child: custom.AnimatedButton(
                                    text: isLogin.value ? 'Sign In' : 'Create Account',
                                    onPressed: isLoading.value ? null : submitForm,
                                    isLoading: isLoading.value,
                                    width: double.infinity,
                                    height: 56,
                                    backgroundColor: Colors.white,
                                    textColor: theme.colorScheme.primary,
                                    borderRadius: 16,
                                    icon: isLogin.value 
                                        ? Icons.login_rounded 
                                        : Icons.person_add_rounded,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 32),
                      
                      // Toggle login/signup with animation
                      FadeInUp(
                        duration: const Duration(milliseconds: 600),
                        delay: const Duration(milliseconds: 1600),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              isLogin.value 
                                  ? "Don't have an account? " 
                                  : "Already have an account? ",
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 16,
                              ),
                            ),
                            GestureDetector(
                              onTap: () => isLogin.value = !isLogin.value,
                              child: Text(
                                isLogin.value ? 'Sign Up' : 'Sign In',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  decoration: TextDecoration.underline,
                                  decorationColor: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Footer with animation
                      FadeIn(
                        duration: const Duration(milliseconds: 600),
                        delay: const Duration(milliseconds: 1800),
                        child: Text(
                          '© ${DateTime.now().year} Options AI • Secure Trading Platform',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.6),
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String? Function(String?) validator,
    required ThemeData theme,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(color: Colors.white.withOpacity(0.8)),
        hintText: 'Enter your ${label.toLowerCase()}',
        hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
        prefixIcon: Icon(icon, color: Colors.white.withOpacity(0.8)),
        suffixIcon: suffixIcon,
        filled: true,
        fillColor: Colors.white.withOpacity(0.1),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: Colors.white, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.red.withOpacity(0.7)),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        errorStyle: const TextStyle(color: Colors.white),
      ),
      validator: validator,
    );
  }
}

// Custom painter for animated trending line effect
class TrendingLinePainter extends CustomPainter {
  final double progress;
  final Color color;

  TrendingLinePainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final path = Path();
    final width = size.width;
    final height = size.height;

    // Create a trending upward line with some curves
    final points = [
      Offset(0, height * 0.8),
      Offset(width * 0.2, height * 0.6),
      Offset(width * 0.4, height * 0.7),
      Offset(width * 0.6, height * 0.4),
      Offset(width * 0.8, height * 0.5),
      Offset(width, height * 0.2),
    ];

    path.moveTo(points.first.dx, points.first.dy);

    for (int i = 1; i < points.length; i++) {
      final current = points[i];
      final previous = points[i - 1];
      
      // Create smooth curves between points
      final controlPoint1 = Offset(
        previous.dx + (current.dx - previous.dx) * 0.3,
        previous.dy,
      );
      final controlPoint2 = Offset(
        previous.dx + (current.dx - previous.dx) * 0.7,
        current.dy,
      );
      
      path.cubicTo(
        controlPoint1.dx, controlPoint1.dy,
        controlPoint2.dx, controlPoint2.dy,
        current.dx, current.dy,
      );
    }

    // Animate the path drawing
    final pathMetric = path.computeMetrics().first;
    final animatedPath = pathMetric.extractPath(0, pathMetric.length * progress);
    
    canvas.drawPath(animatedPath, paint);

    // Draw dots at key points if animation is complete
    if (progress > 0.8) {
      final dotPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      for (final point in points) {
        canvas.drawCircle(point, 1.5, dotPaint);
      }
    }
  }

  @override
  bool shouldRepaint(TrendingLinePainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
