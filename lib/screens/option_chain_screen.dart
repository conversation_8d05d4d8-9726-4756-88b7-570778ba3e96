// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import '../providers/options_provider.dart';
// import '../widgets/option_chain_table.dart';
// import '../widgets/iv_smile_chart.dart';
// import '../widgets/oi_analysis_chart.dart';

// class OptionChainScreen extends StatelessWidget {
//   const OptionChainScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Options Chain'),
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.refresh),
//             onPressed: () {
//               context.read<OptionsProvider>().refresh();
//             },
//           ),
//         ],
//       ),
//       body: Consumer<OptionsProvider>(
//         builder: (context, provider, child) {
//           if (!provider.isInitialized || provider.isLoading) {
//             return const Center(child: CircularProgressIndicator());
//           }

//           if (provider.error != null) {
//             return Center(
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   SelectableText(provider.error!),
//                   const SizedBox(height: 16),
//                   ElevatedButton(onPressed: () => provider.refresh(), child: const Text('Retry')),
//                 ],
//               ),
//             );
//           }

//           return DefaultTabController(
//             length: 3,
//             child: Column(
//               children: [
//                 const TabBar(tabs: [Tab(text: 'Option Chain'), Tab(text: 'IV Analysis'), Tab(text: 'OI Analysis')]),
//                 // if (provider.expiryDates.isNotEmpty)
//                 //   Padding(
//                 //     padding: const EdgeInsets.all(8.0),
//                 //     child: DropdownButtonFormField<String>(
//                 //       value: provider.selectedExpiryDate,
//                 //       items: provider.expiryDates.map((date) => DropdownMenuItem(value: date, child: Text(date))).toList(),
//                 //       onChanged: (date) {
//                 //         if (date != null) {
//                 //           provider.setSelectedExpiryDate(date);
//                 //         }
//                 //       },
//                 //       decoration: const InputDecoration(labelText: 'Expiry Date', border: OutlineInputBorder()),
//                 //     ),
//                 //   ),
//                 if (provider.lastTimestamp != null)
//                   Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Text(
//                       'Last Update: ${provider.lastTimestamp?.toLocal()}}',
//                       style: Theme.of(context).textTheme.bodySmall,
//                     ),
//                   ),
//                 Expanded(
//                   child: TabBarView(
//                     children: [
//                       // Option Chain Tab
//                       const OptionChainTable(),

//                       // IV Analysis Tab
//                       provider.optionChain.isEmpty
//                           ? const Center(child: Text('No data available'))
//                           : IVSmileChart(
//                             data: provider.optionChain,
//                             historicalData: provider.optionChain,
//                             atmStrike: provider.getAtmStrike(),
//                           ),

//                       // OI Analysis Tab
//                       provider.optionChain.isEmpty
//                           ? const Center(child: Text('No data available'))
//                           : OIAnalysisChart(
//                             data: provider.optionChain,
//                             historicalData: provider.optionChain,
//                             atmStrike: provider.getAtmStrike(),
//                           ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
