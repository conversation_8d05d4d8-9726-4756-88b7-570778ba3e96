# Flutter Integration Guide

This guide explains how to integrate the options data API with your Flutter application using Supabase Flutter client.

## Table of Contents
- [Setup](#setup)
- [Data Models](#data-models)
- [API Services](#api-services)
- [Widgets & Charts](#widgets--charts)
- [State Management](#state-management)
- [Error Handling](#error-handling)

## Setup

### 1. Add Dependencies
Add the following to your `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  supabase_flutter: ^1.10.25
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^24.1.44
  syncfusion_flutter_datagrid: ^24.1.44
  provider: ^6.1.1
  intl: ^0.19.0
  freezed_annotation: ^2.4.1

dev_dependencies:
  build_runner: ^2.4.7
  freezed: ^2.4.6
  json_serializable: ^6.7.1
```

### 2. Initialize Supabase

```dart
// lib/config/supabase_config.dart
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: const String.fromEnvironment('SUPABASE_URL'),
      anonKey: const String.fromEnvironment('SUPABASE_ANON_KEY'),
    );
  }

  static SupabaseClient get client => Supabase.instance.client;
}
```

## Data Models

### Option Chain Model

```dart
// lib/models/option_data.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'option_data.freezed.dart';
part 'option_data.g.dart';

@freezed
class OptionData with _$OptionData {
  const factory OptionData({
    required String timestamp,
    required String expiryDate,
    required double strikePrice,
    required String optionType,
    required double spotPrice,
    required double lastPrice,
    required double previousClosePrice,
    required int volume,
    required int previousVolume,
    required int oi,
    required int previousOi,
    required double topBidPrice,
    required double topAskPrice,
    required int topBidQuantity,
    required int topAskQuantity,
    required double iv,
    required double delta,
    required double gamma,
    required double theta,
    required double vega,
  }) = _OptionData;

  factory OptionData.fromJson(Map<String, dynamic> json) =>
      _$OptionDataFromJson(json);
}
```

## API Services

### Options Data Service

```dart
// lib/services/options_service.dart
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/option_data.dart';

class OptionsService {
  final SupabaseClient _client = SupabaseConfig.client;

  Future<List<OptionData>> getLatestOptionChain(String expiryDate) async {
    try {
      final response = await _client
          .from('option_chain_view')
          .select()
          .eq('expiry_date', expiryDate)
          .order('strike_price', ascending: true);

      return (response as List)
          .map((data) => OptionData.fromJson(data))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch option chain: $e');
    }
  }

  Future<List<String>> getExpiryDates() async {
    try {
      final response = await _client
          .from('nifty')
          .select('expiry_date')
          .order('expiry_date')
          .distinct();

      return (response as List).map((data) => data['expiry_date'] as String).toList();
    } catch (e) {
      throw Exception('Failed to fetch expiry dates: $e');
    }
  }

  Stream<List<OptionData>> subscribeToOptionChain(String expiryDate) {
    return _client
        .from('option_chain_view')
        .stream(primaryKey: ['id'])
        .eq('expiry_date', expiryDate)
        .map((data) => data.map((item) => OptionData.fromJson(item)).toList());
  }
}
```

## Widgets & Charts

### 1. Option Chain DataGrid

```dart
// lib/widgets/option_chain_grid.dart
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class OptionChainDataGrid extends StatefulWidget {
  final String expiryDate;

  const OptionChainDataGrid({Key? key, required this.expiryDate}) : super(key: key);

  @override
  _OptionChainDataGridState createState() => _OptionChainDataGridState();
}

class _OptionChainDataGridState extends State<OptionChainDataGrid> {
  late OptionsDataSource _optionsDataSource;
  final OptionsService _service = OptionsService();

  @override
  void initState() {
    super.initState();
    _optionsDataSource = OptionsDataSource([]);
    _loadData();
  }

  Future<void> _loadData() async {
    final data = await _service.getLatestOptionChain(widget.expiryDate);
    setState(() {
      _optionsDataSource = OptionsDataSource(data);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SfDataGrid(
      source: _optionsDataSource,
      columns: [
        GridColumn(
          columnName: 'strikePrice',
          label: Container(
            padding: const EdgeInsets.all(8),
            alignment: Alignment.center,
            child: const Text('Strike'),
          ),
        ),
        GridColumn(
          columnName: 'callPrice',
          label: Container(
            padding: const EdgeInsets.all(8),
            alignment: Alignment.center,
            child: const Text('CE Price'),
          ),
        ),
        // Add more columns as needed
      ],
    );
  }
}
```

### 2. IV Smile Chart

```dart
// lib/widgets/iv_smile_chart.dart
import 'package:fl_chart/fl_chart.dart';

class IVSmileChart extends StatelessWidget {
  final String expiryDate;
  final List<OptionData> data;

  const IVSmileChart({
    Key? key,
    required this.expiryDate,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: LineChart(
        LineChartData(
          lineBarsData: [
            // CE Line
            LineChartBarData(
              spots: data
                  .where((d) => d.optionType == 'CE')
                  .map((d) => FlSpot(d.strikePrice, d.iv))
                  .toList(),
              color: Colors.blue,
              dotData: FlDotData(show: false),
            ),
            // PE Line
            LineChartBarData(
              spots: data
                  .where((d) => d.optionType == 'PE')
                  .map((d) => FlSpot(d.strikePrice, d.iv))
                  .toList(),
              color: Colors.red,
              dotData: FlDotData(show: false),
            ),
          ],
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 50,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
```

### 3. OI Analysis Chart

```dart
// lib/widgets/oi_analysis_chart.dart
import 'package:syncfusion_flutter_charts/charts.dart';

class OIAnalysisChart extends StatelessWidget {
  final String expiryDate;
  final List<OptionData> data;

  const OIAnalysisChart({
    Key? key,
    required this.expiryDate,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SfCartesianChart(
      primaryXAxis: NumericAxis(
        title: AxisTitle(text: 'Strike Price'),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: 'Open Interest'),
      ),
      series: <ChartSeries>[
        ColumnSeries<OptionData, double>(
          dataSource: data.where((d) => d.optionType == 'CE').toList(),
          xValueMapper: (OptionData data, _) => data.strikePrice,
          yValueMapper: (OptionData data, _) => data.oi,
          name: 'CE OI',
          color: Colors.blue,
        ),
        ColumnSeries<OptionData, double>(
          dataSource: data.where((d) => d.optionType == 'PE').toList(),
          xValueMapper: (OptionData data, _) => data.strikePrice,
          yValueMapper: (OptionData data, _) => data.oi,
          name: 'PE OI',
          color: Colors.red,
        ),
      ],
    );
  }
}
```

## State Management

### Options Provider

```dart
// lib/providers/options_provider.dart
import 'package:flutter/foundation.dart';
import '../services/options_service.dart';

class OptionsProvider with ChangeNotifier {
  final OptionsService _service = OptionsService();
  List<OptionData> _optionChainData = [];
  List<String> _expiryDates = [];
  String? _selectedExpiryDate;
  bool _isLoading = false;
  String? _error;

  List<OptionData> get optionChainData => _optionChainData;
  List<String> get expiryDates => _expiryDates;
  String? get selectedExpiryDate => _selectedExpiryDate;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadExpiryDates() async {
    try {
      _isLoading = true;
      notifyListeners();

      _expiryDates = await _service.getExpiryDates();
      if (_expiryDates.isNotEmpty) {
        _selectedExpiryDate = _expiryDates.first;
        await loadOptionChain();
      }
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadOptionChain() async {
    if (_selectedExpiryDate == null) return;

    try {
      _isLoading = true;
      notifyListeners();

      _optionChainData = await _service.getLatestOptionChain(_selectedExpiryDate!);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void setSelectedExpiryDate(String date) {
    _selectedExpiryDate = date;
    loadOptionChain();
  }
}
```

## Main Screen Example

```dart
// lib/screens/options_dashboard.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class OptionsDashboard extends StatelessWidget {
  const OptionsDashboard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => OptionsProvider()..loadExpiryDates(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Options Analysis'),
        ),
        body: Consumer<OptionsProvider>(
          builder: (context, provider, _) {
            if (provider.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (provider.error != null) {
              return Center(child: Text('Error: ${provider.error}'));
            }

            return Column(
              children: [
                // Expiry Date Selector
                DropdownButton<String>(
                  value: provider.selectedExpiryDate,
                  items: provider.expiryDates
                      .map((date) => DropdownMenuItem(
                            value: date,
                            child: Text(date),
                          ))
                      .toList(),
                  onChanged: (date) {
                    if (date != null) {
                      provider.setSelectedExpiryDate(date);
                    }
                  },
                ),
                
                // Option Chain Grid
                Expanded(
                  child: DefaultTabController(
                    length: 3,
                    child: Column(
                      children: [
                        const TabBar(
                          tabs: [
                            Tab(text: 'Option Chain'),
                            Tab(text: 'IV Analysis'),
                            Tab(text: 'OI Analysis'),
                          ],
                        ),
                        Expanded(
                          child: TabBarView(
                            children: [
                              OptionChainDataGrid(
                                expiryDate: provider.selectedExpiryDate!,
                              ),
                              IVSmileChart(
                                expiryDate: provider.selectedExpiryDate!,
                                data: provider.optionChainData,
                              ),
                              OIAnalysisChart(
                                expiryDate: provider.selectedExpiryDate!,
                                data: provider.optionChainData,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
```

## Error Handling

```dart
// lib/utils/error_handler.dart
class ErrorHandler {
  static String handleSupabaseError(dynamic error) {
    if (error is PostgrestError) {
      switch (error.code) {
        case 'PGRST301':
          return 'Database connection error';
        case 'PGRST204':
          return 'No data found';
        default:
          return 'Database error: ${error.message}';
      }
    }
    return 'An unexpected error occurred';
  }
}
```

## Best Practices

1. **State Management**
   - Use Provider or Riverpod for state management
   - Implement proper loading and error states
   - Cache data when appropriate

2. **Performance**
   - Implement pagination for large datasets
   - Use const constructors where possible
   - Implement proper widget rebuilding optimization

3. **Error Handling**
   - Implement proper error boundaries
   - Show user-friendly error messages
   - Log errors for debugging

4. **UI/UX**
   - Follow Material Design guidelines
   - Implement responsive layouts
   - Add proper loading indicators

For more information, refer to:
- [Supabase Flutter Documentation](https://supabase.com/docs/reference/dart/introduction)
- [FL Chart Documentation](https://pub.dev/packages/fl_chart)
- [Syncfusion Flutter Documentation](https://help.syncfusion.com/flutter/introduction/overview) 