# Flutter App Examples

## Common Use Cases

### 1. Display Option Chain Table

```dart
class OptionChainScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<OptionChainProvider>(
      builder: (context, provider, child) {
        if (provider.loading) {
          return Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error: ${provider.error}'),
                ElevatedButton(
                  onPressed: () => provider.fetchOptionChain(),
                  child: Text('Retry'),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            ExpiryDateSelector(
              selectedDate: provider.selectedExpiry,
              onChanged: (date) => provider.setExpiry(date),
            ),
            Expanded(
              child: OptionChainTable(data: provider.optionChain),
            ),
          ],
        );
      },
    );
  }
}

class ExpiryDateSelector extends StatelessWidget {
  final DateTime selectedDate;
  final ValueChanged<DateTime> onChanged;

  const ExpiryDateSelector({
    Key? key,
    required this.selectedDate,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.0),
      child: DropdownButtonFormField<DateTime>(
        value: selectedDate,
        items: _getExpiryDates().map((date) {
          return DropdownMenuItem(
            value: date,
            child: Text(DateFormat('dd MMM yyyy').format(date)),
          );
        }).toList(),
        onChanged: (date) {
          if (date != null) onChanged(date);
        },
        decoration: InputDecoration(
          labelText: 'Expiry Date',
          border: OutlineInputBorder(),
        ),
      ),
    );
  }

  List<DateTime> _getExpiryDates() {
    // Implement logic to get available expiry dates
    return [];
  }
}
```

### 2. Implement IV Smile Chart

```dart
class IVAnalysisScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<OptionChainProvider>(
      builder: (context, provider, child) {
        if (provider.loading) {
          return Center(child: CircularProgressIndicator());
        }

        final ceData = provider.optionChain
            .where((option) => option.optionType == 'CE')
            .toList();
        final peData = provider.optionChain
            .where((option) => option.optionType == 'PE')
            .toList();

        return Column(
          children: [
            Text(
              'IV Smile Analysis',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            Expanded(
              child: SfCartesianChart(
                legend: Legend(isVisible: true),
                primaryXAxis: NumericAxis(
                  title: AxisTitle(text: 'Strike Price'),
                ),
                primaryYAxis: NumericAxis(
                  title: AxisTitle(text: 'Implied Volatility'),
                ),
                series: <ChartSeries>[
                  LineSeries<OptionData, double>(
                    name: 'CE',
                    dataSource: ceData,
                    xValueMapper: (OptionData data, _) => data.strikePrice,
                    yValueMapper: (OptionData data, _) => data.impliedVolatility,
                    color: Colors.green,
                  ),
                  LineSeries<OptionData, double>(
                    name: 'PE',
                    dataSource: peData,
                    xValueMapper: (OptionData data, _) => data.strikePrice,
                    yValueMapper: (OptionData data, _) => data.impliedVolatility,
                    color: Colors.red,
                  ),
                ],
                tooltipBehavior: TooltipBehavior(enable: true),
                crosshairBehavior: CrosshairBehavior(enable: true),
              ),
            ),
          ],
        );
      },
    );
  }
}
```

### 3. OI Analysis Chart

```dart
class OIAnalysisScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<OptionChainProvider>(
      builder: (context, provider, child) {
        if (provider.loading) {
          return Center(child: CircularProgressIndicator());
        }

        return Column(
          children: [
            Text(
              'Open Interest Analysis',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            Expanded(
              child: SfCartesianChart(
                primaryXAxis: NumericAxis(
                  title: AxisTitle(text: 'Strike Price'),
                ),
                primaryYAxis: NumericAxis(
                  title: AxisTitle(text: 'Open Interest'),
                ),
                series: <ChartSeries>[
                  ColumnSeries<OptionData, double>(
                    name: 'CE OI',
                    dataSource: provider.optionChain
                        .where((option) => option.optionType == 'CE')
                        .toList(),
                    xValueMapper: (OptionData data, _) => data.strikePrice,
                    yValueMapper: (OptionData data, _) => data.openInterest,
                    color: Colors.green.withOpacity(0.7),
                  ),
                  ColumnSeries<OptionData, double>(
                    name: 'PE OI',
                    dataSource: provider.optionChain
                        .where((option) => option.optionType == 'PE')
                        .toList(),
                    xValueMapper: (OptionData data, _) => data.strikePrice,
                    yValueMapper: (OptionData data, _) => data.openInterest,
                    color: Colors.red.withOpacity(0.7),
                  ),
                ],
                tooltipBehavior: TooltipBehavior(enable: true),
              ),
            ),
          ],
        );
      },
    );
  }
}
```

### 4. Real-time Price Updates

```dart
class PriceUpdateWidget extends StatefulWidget {
  final OptionData optionData;

  const PriceUpdateWidget({Key? key, required this.optionData}) : super(key: key);

  @override
  _PriceUpdateWidgetState createState() => _PriceUpdateWidgetState();
}

class _PriceUpdateWidgetState extends State<PriceUpdateWidget> {
  late double _previousPrice;
  Color _priceColor = Colors.black;

  @override
  void initState() {
    super.initState();
    _previousPrice = widget.optionData.lastPrice;
  }

  @override
  void didUpdateWidget(PriceUpdateWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.optionData.lastPrice != _previousPrice) {
      setState(() {
        _priceColor = widget.optionData.lastPrice > _previousPrice
            ? Colors.green
            : Colors.red;
        _previousPrice = widget.optionData.lastPrice;
      });
      _resetColor();
    }
  }

  void _resetColor() {
    Future.delayed(Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _priceColor = Colors.black;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedDefaultTextStyle(
      duration: Duration(milliseconds: 300),
      style: TextStyle(
        color: _priceColor,
        fontWeight: FontWeight.bold,
      ),
      child: Text(
        widget.optionData.lastPrice.toStringAsFixed(2),
      ),
    );
  }
}
```

### 5. Greeks Analysis Dashboard

```dart
class GreeksAnalysisScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<OptionChainProvider>(
      builder: (context, provider, child) {
        if (provider.loading) {
          return Center(child: CircularProgressIndicator());
        }

        return Column(
          children: [
            Text(
              'Greeks Analysis',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                children: [
                  _buildGreekChart(
                    'Delta',
                    provider.optionChain,
                    (data) => data.delta,
                  ),
                  _buildGreekChart(
                    'Theta',
                    provider.optionChain,
                    (data) => data.theta,
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildGreekChart(
    String title,
    List<OptionData> data,
    double Function(OptionData) valueMapper,
  ) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: Column(
          children: [
            Text(title),
            Expanded(
              child: SfCartesianChart(
                primaryXAxis: NumericAxis(
                  title: AxisTitle(text: 'Strike Price'),
                ),
                primaryYAxis: NumericAxis(
                  title: AxisTitle(text: title),
                ),
                series: <ChartSeries>[
                  LineSeries<OptionData, double>(
                    dataSource: data,
                    xValueMapper: (OptionData data, _) => data.strikePrice,
                    yValueMapper: (OptionData data, _) => valueMapper(data),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

### 6. Performance Optimized Option Chain

```dart
class OptimizedOptionChainTable extends StatelessWidget {
  final List<OptionData> data;

  const OptimizedOptionChainTable({Key? key, required this.data})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: data.length,
      itemBuilder: (context, index) {
        final option = data[index];
        return Card(
          child: ListTile(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(option.strikePrice.toString()),
                PriceUpdateWidget(optionData: option),
              ],
            ),
            subtitle: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('OI: ${option.openInterest}'),
                Text('IV: ${option.impliedVolatility.toStringAsFixed(2)}%'),
              ],
            ),
          ),
        );
      },
    );
  }
}
```

### 7. Option Analysis Screen

```dart
class OptionAnalysisScreen extends StatefulWidget {
  @override
  _OptionAnalysisScreenState createState() => _OptionAnalysisScreenState();
}

class _OptionAnalysisScreenState extends State<OptionAnalysisScreen> {
  DateTime? _startTime;
  DateTime? _endTime;

  @override
  void initState() {
    super.initState();
    // Default to last 24 hours
    _endTime = DateTime.now();
    _startTime = _endTime!.subtract(Duration(hours: 24));
    _loadData();
  }

  Future<void> _loadData() async {
    final provider = context.read<OptionChainProvider>();
    await provider.fetchLatestOptionChain();
    if (_startTime != null && _endTime != null) {
      await provider.fetchHistoricalData(_startTime!, _endTime!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OptionChainProvider>(
      builder: (context, provider, child) {
        if (provider.loading) {
          return Center(child: CircularProgressIndicator());
        }

        return Column(
          children: [
            _buildTimeRangeSelector(),
            Expanded(
              child: DefaultTabController(
                length: 2,
                child: Column(
                  children: [
                    TabBar(
                      tabs: [
                        Tab(text: 'Latest Data'),
                        Tab(text: 'Historical Analysis'),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          _buildLatestDataView(provider),
                          _buildHistoricalAnalysis(provider),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTimeRangeSelector() {
    return Padding(
      padding: EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: TextButton.icon(
              icon: Icon(Icons.calendar_today),
              label: Text(_startTime == null
                  ? 'Start Time'
                  : DateFormat('dd MMM HH:mm').format(_startTime!)),
              onPressed: () async {
                final dateTime = await showDateTimePicker(
                  context: context,
                  initialDate: _startTime ?? DateTime.now(),
                  firstDate: DateTime.now().subtract(Duration(days: 30)),
                  lastDate: DateTime.now(),
                );
                if (dateTime != null) {
                  setState(() {
                    _startTime = dateTime;
                  });
                  _loadData();
                }
              },
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: TextButton.icon(
              icon: Icon(Icons.calendar_today),
              label: Text(_endTime == null
                  ? 'End Time'
                  : DateFormat('dd MMM HH:mm').format(_endTime!)),
              onPressed: () async {
                final dateTime = await showDateTimePicker(
                  context: context,
                  initialDate: _endTime ?? DateTime.now(),
                  firstDate: DateTime.now().subtract(Duration(days: 30)),
                  lastDate: DateTime.now(),
                );
                if (dateTime != null) {
                  setState(() {
                    _endTime = dateTime;
                  });
                  _loadData();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLatestDataView(OptionChainProvider provider) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            'Latest Option Chain Data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ),
        Expanded(
          child: OptimizedOptionChainTable(
            data: provider.latestOptionChain,
          ),
        ),
      ],
    );
  }

  Widget _buildHistoricalAnalysis(OptionChainProvider provider) {
    if (provider.historicalData.isEmpty) {
      return Center(
        child: Text('No historical data available for the selected time range'),
      );
    }

    // Get a sample strike price and option type for analysis
    final sampleOption = provider.latestOptionChain.first;
    final priceChanges = provider.getPriceChanges(
      sampleOption.strikePrice,
      sampleOption.optionType,
    );

    return ListView(
      padding: EdgeInsets.all(16.0),
      children: [
        Text(
          'Price Change Analysis',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        SizedBox(
          height: 300,
          child: PriceChangeAnalysisChart(priceChanges: priceChanges),
        ),
        SizedBox(height: 24),
        Text(
          'Historical IV Analysis',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        SizedBox(
          height: 300,
          child: _buildHistoricalIVChart(provider),
        ),
      ],
    );
  }

  Widget _buildHistoricalIVChart(OptionChainProvider provider) {
    final timestamps = provider.historicalData.keys.toList()..sort();
    final ivData = timestamps.map((timestamp) {
      final options = provider.historicalData[timestamp]!;
      final avgIV = options.fold<double>(
            0,
            (sum, opt) => sum + opt.impliedVolatility,
          ) /
          options.length;
      return MapEntry(timestamp, avgIV);
    }).toList();

    return SfCartesianChart(
      primaryXAxis: DateTimeAxis(
        title: AxisTitle(text: 'Time'),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: 'Average IV'),
      ),
      series: <ChartSeries>[
        LineSeries<MapEntry<DateTime, double>, DateTime>(
          name: 'Average IV',
          dataSource: ivData,
          xValueMapper: (entry, _) => entry.key,
          yValueMapper: (entry, _) => entry.value,
        ),
      ],
      tooltipBehavior: TooltipBehavior(enable: true),
    );
  }
}

// Helper widget for date and time selection
Future<DateTime?> showDateTimePicker({
  required BuildContext context,
  required DateTime initialDate,
  required DateTime firstDate,
  required DateTime lastDate,
}) async {
  final date = await showDatePicker(
    context: context,
    initialDate: initialDate,
    firstDate: firstDate,
    lastDate: lastDate,
  );
  if (date == null) return null;

  final time = await showTimePicker(
    context: context,
    initialTime: TimeOfDay.fromDateTime(initialDate),
  );
  if (time == null) return null;

  return DateTime(
    date.year,
    date.month,
    date.day,
    time.hour,
    time.minute,
  );
}
```

This example screen provides:
1. Time range selection for historical data analysis
2. Tab view to switch between latest and historical data
3. Latest option chain data display
4. Historical price change analysis with charts
5. Historical IV analysis with charts

Key features:
- Shows the most recent data in the option chain table
- Allows analysis of historical data within a selected time range
- Visualizes price changes and IV trends over time
- Provides an intuitive UI for time range selection
- Handles loading states and error cases

## Tips and Best Practices

1. **State Management**
   - Use `Provider` for simple state management
   - Consider using `Riverpod` for more complex state management needs
   - Implement proper error handling and loading states

2. **Performance**
   - Use `const` constructors where possible
   - Implement pagination for large datasets
   - Cache data locally using `SharedPreferences`
   - Use `ListView.builder` instead of Column for long lists

3. **Real-time Updates**
   - Handle WebSocket reconnection
   - Implement proper cleanup in `dispose` methods
   - Use debouncing for frequent updates

4. **Error Handling**
   - Implement proper error boundaries
   - Show user-friendly error messages
   - Provide retry mechanisms

5. **Testing**
   - Write widget tests for UI components
   - Write unit tests for business logic
   - Use mocks for API calls 