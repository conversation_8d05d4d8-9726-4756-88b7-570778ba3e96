name: market_magic_ai
description: "Advanced options analytics and visualization platform."

publish_to: 'none' 

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  animated_to: ^0.4.0
  cupertino_icons: ^1.0.8
  flutter:
    sdk: flutter
  flutter_dotenv: ^5.2.1
  http: ^1.3.0
  supabase_flutter: ^2.8.4
  fl_chart: ^1.0.0
  intl: ^0.20.2
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  data_table_2: ^2.5.10
  syncfusion_flutter_charts: ^30.1.41
  syncfusion_flutter_datagrid: ^30.1.41
  go_router: ^16.0.0
  flutter_fullscreen: ^1.1.0
  flex_color_scheme: ^8.2.0
  hooks_riverpod: ^2.6.1
  flutter_hooks: 0.21.2
  riverpod_annotation: ^2.6.1
  fluttertoast: ^8.2.12
  google_fonts: ^6.2.1
  animations: ^2.0.11
  animate_do: ^4.2.0
  lottie: ^3.1.2
  rive: ^0.13.20
  # Authentication packages
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.2
  firebase_auth: ^5.3.1
  firebase_core: ^3.6.0
  # Phone verification
  pinput: ^5.0.0
  # Biometric authentication
  local_auth: ^2.3.0
  # State management helpers
  shared_preferences: ^2.3.2
  # Debug console
  logarte: ^1.1.0
  

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.7
  riverpod_generator: ^2.6.5
  riverpod_lint: ^2.6.5
  freezed: ^3.0.6
  json_serializable: ^6.7.1
  flutter_lints: ^6.0.0

  

flutter:
  uses-material-design: true
  
  assets:
    - assets/dotenv
  
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
