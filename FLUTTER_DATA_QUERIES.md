# Flutter Data Queries Guide

This guide provides the SQL views and queries needed to create effective data visualizations in your Flutter application. These views will help optimize data fetching and make it easier to create charts and tables.

## Database Views

### 1. Option Chain View
```sql
CREATE OR REPLACE VIEW option_chain_view AS
WITH latest_data AS (
    SELECT DISTINCT ON (expiry_date, strike_price, option_type)
        *
    FROM nifty
    ORDER BY expiry_date, strike_price, option_type, timestamp DESC
)
SELECT
    timestamp,
    expiry_date,
    strike_price,
    option_type,
    spot_price,
    last_price,
    previous_close_price,
    volume,
    oi,
    previous_oi,
    iv,
    delta,
    gamma,
    theta,
    vega,
    (oi - previous_oi) as oi_change,
    ((last_price - previous_close_price) / NULLIF(previous_close_price, 0) * 100) as price_change_percent
FROM latest_data;

-- Usage in Flutter:
/*
final response = await supabase
    .from('option_chain_view')
    .select()
    .eq('expiry_date', selectedExpiry)
    .order('strike_price', ascending: true);
*/
```

### 2. IV Surface View
```sql
CREATE OR REPLACE VIEW iv_surface_view AS
WITH latest_data AS (
    SELECT DISTINCT ON (expiry_date, strike_price, option_type)
        *
    FROM nifty
    ORDER BY expiry_date, strike_price, option_type, timestamp DESC
)
SELECT
    expiry_date,
    strike_price,
    option_type,
    iv,
    spot_price,
    (strike_price - spot_price) as moneyness
FROM latest_data
WHERE iv > 0;

-- Usage in Flutter:
/*
final response = await supabase
    .from('iv_surface_view')
    .select()
    .eq('expiry_date', selectedExpiry)
    .order('strike_price', ascending: true);
*/
```

### 3. Greeks Analysis View
```sql
CREATE OR REPLACE VIEW greeks_analysis_view AS
WITH latest_data AS (
    SELECT DISTINCT ON (expiry_date, strike_price, option_type)
        *
    FROM nifty
    ORDER BY expiry_date, strike_price, option_type, timestamp DESC
)
SELECT
    expiry_date,
    strike_price,
    option_type,
    delta,
    gamma,
    theta,
    vega,
    spot_price,
    (strike_price - spot_price) as moneyness
FROM latest_data
WHERE delta IS NOT NULL;

-- Usage in Flutter:
/*
final response = await supabase
    .from('greeks_analysis_view')
    .select()
    .eq('expiry_date', selectedExpiry)
    .order('strike_price', ascending: true);
*/
```

### 4. OI Analysis View
```sql
CREATE OR REPLACE VIEW oi_analysis_view AS
WITH latest_data AS (
    SELECT DISTINCT ON (expiry_date, strike_price, option_type)
        *
    FROM nifty
    ORDER BY expiry_date, strike_price, option_type, timestamp DESC
)
SELECT
    expiry_date,
    strike_price,
    option_type,
    oi,
    previous_oi,
    (oi - previous_oi) as oi_change,
    volume,
    spot_price
FROM latest_data;

-- Usage in Flutter:
/*
final response = await supabase
    .from('oi_analysis_view')
    .select()
    .eq('expiry_date', selectedExpiry)
    .order('strike_price', ascending: true);
*/
```

### 5. Historical Price View
```sql
CREATE OR REPLACE VIEW historical_price_view AS
SELECT
    date_trunc('minute', timestamp) as time_bucket,
    expiry_date,
    strike_price,
    option_type,
    AVG(last_price) as avg_price,
    MAX(last_price) as high_price,
    MIN(last_price) as low_price,
    FIRST_VALUE(last_price) OVER (PARTITION BY date_trunc('minute', timestamp), expiry_date, strike_price, option_type ORDER BY timestamp) as open_price,
    LAST_VALUE(last_price) OVER (PARTITION BY date_trunc('minute', timestamp), expiry_date, strike_price, option_type ORDER BY timestamp) as close_price,
    SUM(volume) as volume
FROM nifty
GROUP BY date_trunc('minute', timestamp), expiry_date, strike_price, option_type;

-- Usage in Flutter:
/*
final response = await supabase
    .from('historical_price_view')
    .select()
    .eq('expiry_date', selectedExpiry)
    .eq('strike_price', selectedStrike)
    .eq('option_type', selectedType)
    .order('time_bucket', ascending: true);
*/
```

## Common Queries for Visualization

### 1. ATM Strike Price
```sql
CREATE OR REPLACE FUNCTION get_atm_strike(p_expiry_date text)
RETURNS numeric AS $$
WITH latest_spot AS (
    SELECT DISTINCT ON (expiry_date) 
        spot_price, 
        strike_price,
        ABS(strike_price - spot_price) as price_diff
    FROM nifty
    WHERE expiry_date = p_expiry_date
    ORDER BY expiry_date, timestamp DESC
)
SELECT strike_price
FROM latest_spot
ORDER BY price_diff ASC
LIMIT 1;
$$ LANGUAGE SQL;

-- Usage in Flutter:
/*
final response = await supabase
    .rpc('get_atm_strike', params: {'p_expiry_date': selectedExpiry})
    .single();
*/
```

### 2. PCR (Put-Call Ratio) Analysis
```sql
CREATE OR REPLACE VIEW pcr_analysis_view AS
WITH latest_data AS (
    SELECT DISTINCT ON (expiry_date, strike_price, option_type)
        *
    FROM nifty
    ORDER BY expiry_date, strike_price, option_type, timestamp DESC
)
SELECT
    expiry_date,
    SUM(CASE WHEN option_type = 'PE' THEN oi ELSE 0 END) as total_put_oi,
    SUM(CASE WHEN option_type = 'CE' THEN oi ELSE 0 END) as total_call_oi,
    ROUND(CAST(SUM(CASE WHEN option_type = 'PE' THEN oi ELSE 0 END) AS FLOAT) / 
          NULLIF(SUM(CASE WHEN option_type = 'CE' THEN oi ELSE 0 END), 0), 2) as pcr
FROM latest_data
GROUP BY expiry_date;

-- Usage in Flutter:
/*
final response = await supabase
    .from('pcr_analysis_view')
    .select()
    .order('expiry_date', ascending: true);
*/
```

## Flutter Widget Examples

### 1. Option Chain Table
```dart
Future<List<Map<String, dynamic>>> fetchOptionChain(String expiryDate) async {
  final response = await supabase
      .from('option_chain_view')
      .select()
      .eq('expiry_date', expiryDate)
      .order('strike_price', ascending: true);
  
  return response;
}

// Usage in widget:
FutureBuilder<List<Map<String, dynamic>>>(
  future: fetchOptionChain(selectedExpiry),
  builder: (context, snapshot) {
    if (!snapshot.hasData) return CircularProgressIndicator();
    
    return SfDataGrid(
      source: OptionChainDataSource(snapshot.data!),
      columns: [
        GridColumn(columnName: 'strike_price', label: Text('Strike')),
        GridColumn(columnName: 'last_price', label: Text('LTP')),
        GridColumn(columnName: 'oi', label: Text('OI')),
        GridColumn(columnName: 'iv', label: Text('IV')),
      ]
    );
  }
)
```

### 2. IV Smile Chart
```dart
Future<List<Map<String, dynamic>>> fetchIVData(String expiryDate) async {
  final response = await supabase
      .from('iv_surface_view')
      .select()
      .eq('expiry_date', expiryDate)
      .order('strike_price', ascending: true);
  
  return response;
}

// Usage in widget:
SfCartesianChart(
  primaryXAxis: NumericAxis(title: AxisTitle(text: 'Strike Price')),
  primaryYAxis: NumericAxis(title: AxisTitle(text: 'IV')),
  series: <ChartSeries>[
    LineSeries<Map<String, dynamic>, num>(
      dataSource: ivData,
      xValueMapper: (Map<String, dynamic> data, _) => data['strike_price'],
      yValueMapper: (Map<String, dynamic> data, _) => data['iv'],
    )
  ]
)
```

### 3. OI Change Heatmap
```dart
Future<List<Map<String, dynamic>>> fetchOIData(String expiryDate) async {
  final response = await supabase
      .from('oi_analysis_view')
      .select()
      .eq('expiry_date', expiryDate)
      .order('strike_price', ascending: true);
  
  return response;
}

// Usage in widget:
HeatMap(
  data: oiData,
  colorScale: ColorScale(
    min: minOIChange,
    max: maxOIChange,
    colors: [Colors.red, Colors.white, Colors.green],
  ),
  xAxis: strikes,
  yAxis: ['CE', 'PE'],
)
```

## Best Practices

1. **Data Caching**
   - Cache the option chain data locally
   - Implement periodic refresh (e.g., every 1 minute)
   - Use StreamBuilder for real-time updates

2. **Performance**
   - Use pagination for large datasets
   - Implement lazy loading for historical data
   - Cache images and computed values

3. **Error Handling**
   - Implement retry logic for failed requests
   - Show appropriate error messages
   - Provide fallback UI when data is unavailable

4. **State Management**
   - Use Provider/Riverpod for state management
   - Separate data fetching logic from UI
   - Implement proper loading states

## Example Provider
```dart
class OptionsDataProvider extends ChangeNotifier {
  final Supabase _supabase;
  List<String> _expiryDates = [];
  String? _selectedExpiry;
  List<Map<String, dynamic>> _optionChainData = [];
  bool _isLoading = false;
  
  Future<void> fetchExpiryDates() async {
    try {
      _isLoading = true;
      notifyListeners();
      
      final response = await _supabase
          .from('option_chain_view')
          .select('expiry_date')
          .distinct();
      
      _expiryDates = response.map((r) => r['expiry_date'].toString()).toList();
      if (_expiryDates.isNotEmpty) {
        _selectedExpiry = _expiryDates.first;
        await fetchOptionChain();
      }
    } catch (e) {
      print('Error fetching expiry dates: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  Future<void> fetchOptionChain() async {
    if (_selectedExpiry == null) return;
    
    try {
      _isLoading = true;
      notifyListeners();
      
      final response = await _supabase
          .from('option_chain_view')
          .select()
          .eq('expiry_date', _selectedExpiry)
          .order('strike_price', ascending: true);
      
      _optionChainData = response;
    } catch (e) {
      print('Error fetching option chain: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
```

For more information on implementing these visualizations, refer to:
- [Syncfusion Flutter Charts](https://pub.dev/packages/syncfusion_flutter_charts)
- [FL Chart](https://pub.dev/packages/fl_chart)
- [Supabase Flutter](https://supabase.com/docs/reference/dart/introduction) 