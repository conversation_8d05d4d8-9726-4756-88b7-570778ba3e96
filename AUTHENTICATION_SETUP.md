# Enhanced Authentication Setup Guide

This guide will help you integrate Google, Apple, Phone, and Email authentication with biometric support in your Options AI Flutter application.

## 📋 Table of Contents

1. [Dependencies Setup](#dependencies-setup)
2. [Firebase Setup](#firebase-setup)
3. [Google Authentication](#google-authentication)
4. [Apple Authentication](#apple-authentication)
5. [Phone Authentication](#phone-authentication)
6. [Biometric Authentication](#biometric-authentication)
7. [Supabase Integration](#supabase-integration)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)

## 🔧 Dependencies Setup

The following dependencies have been added to your `pubspec.yaml`:

```yaml
dependencies:
  # Authentication packages
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.2
  firebase_auth: ^5.3.1
  firebase_core: ^3.6.0
  # Phone verification
  pinput: ^5.0.0
  # Biometric authentication
  local_auth: ^2.3.0
  # State management helpers
  shared_preferences: ^2.3.2
```

Run the following command to install dependencies:

```bash
flutter pub get
```

## 🔥 Firebase Setup

### 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or use an existing one
3. Enable Authentication in the Firebase Console
4. Configure sign-in methods:
   - Email/Password
   - Google
   - Phone
   - Apple (for iOS)

### 2. Configure Firebase for Flutter

Run the FlutterFire CLI to configure Firebase:

```bash
# Install FlutterFire CLI
dart pub global activate flutterfire_cli

# Configure Firebase for your project
flutterfire configure
```

This will create/update the `firebase_options.dart` file with your project configuration.

### 3. Update firebase_options.dart

Replace the placeholder values in `lib/firebase_options.dart` with your actual Firebase configuration values.

## 🔐 Google Authentication

### 1. Firebase Console Setup

1. In Firebase Console, go to Authentication > Sign-in method
2. Enable Google sign-in
3. Add your app's SHA-1 fingerprint for Android

### 2. Android Configuration

Add to `android/app/build.gradle`:

```gradle
android {
    ...
    signingConfigs {
        debug {
            // Add your keystore configuration
        }
    }
}
```

Get SHA-1 fingerprint:
```bash
cd android
./gradlew signingReport
```

### 3. iOS Configuration

1. Add GoogleService-Info.plist to iOS runner
2. Add URL scheme to `ios/Runner/Info.plist`:

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>REVERSED_CLIENT_ID</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>YOUR_REVERSED_CLIENT_ID</string>
        </array>
    </dict>
</array>
```

### 4. Web Configuration

Add to `web/index.html`:

```html
<script src="https://apis.google.com/js/platform.js" async defer></script>
<meta name="google-signin-client_id" content="YOUR_WEB_CLIENT_ID">
```

## 🍎 Apple Authentication

### 1. Apple Developer Setup

1. Enable Sign in with Apple capability in Xcode
2. Configure Sign in with Apple in Apple Developer Console
3. Add domain and return URLs

### 2. iOS Configuration

Add to `ios/Runner/Runner.entitlements`:

```xml
<key>com.apple.developer.applesignin</key>
<array>
    <string>Default</string>
</array>
```

### 3. Web Configuration

Update the Apple Sign In configuration in `lib/services/enhanced_auth_service.dart`:

```dart
webAuthenticationOptions: kIsWeb 
  ? WebAuthenticationOptions(
      clientId: 'your-web-client-id',
      redirectUri: Uri.parse('https://your-domain.com/auth/callback'),
    )
  : null,
```

## 📱 Phone Authentication

### 1. Firebase Console Setup

1. Enable Phone authentication in Firebase Console
2. Add test phone numbers for development (optional)

### 2. Android Configuration

No additional configuration needed for basic phone auth.

### 3. iOS Configuration

Enable push notifications capability in Xcode (required for phone auth).

### 4. reCAPTCHA for Web

Phone authentication on web requires reCAPTCHA verification.

## 👆 Biometric Authentication

### 1. Android Configuration

Add to `android/app/src/main/AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.USE_FINGERPRINT" />
<uses-permission android:name="android.permission.USE_BIOMETRIC" />
```

### 2. iOS Configuration

Add to `ios/Runner/Info.plist`:

```xml
<key>NSFaceIDUsageDescription</key>
<string>Use Face ID to authenticate securely</string>
```

## 🗄️ Supabase Integration

### 1. Database Schema

Create the following table in Supabase:

```sql
-- User profiles table
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  phone TEXT,
  is_onboarded BOOLEAN DEFAULT FALSE,
  subscription_tier TEXT DEFAULT 'free',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  preferences JSONB DEFAULT '{}'
);

-- RLS Policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON user_profiles
  FOR INSERT WITH CHECK (auth.uid() = id);
```

### 2. Environment Variables

Update `assets/dotenv`:

```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🧪 Testing

### 1. Test Authentication Methods

Test each authentication method:

1. **Email/Password**: Create account and sign in
2. **Google**: Test on different platforms
3. **Apple**: Test on iOS/macOS devices
4. **Phone**: Test with real phone numbers
5. **Biometric**: Test on devices with biometric capabilities

### 2. Test User Flow

1. Sign up with different methods
2. Complete onboarding process
3. Sign out and sign back in
4. Test password reset (email only)
5. Test account deletion

### 3. Test Edge Cases

1. Network connectivity issues
2. Invalid credentials
3. Biometric authentication failures
4. Phone verification timeouts

## 🚀 Usage Examples

### Using Enhanced Authentication

```dart
// Get the enhanced auth provider
final enhancedAuth = ref.read(enhancedAuthNotifierProvider.notifier);

// Sign in with Google
await enhancedAuth.signInWithGoogle();

// Sign in with Apple
await enhancedAuth.signInWithApple();

// Send phone verification
await enhancedAuth.sendPhoneVerificationCode('+**********');

// Verify phone code
await enhancedAuth.verifyPhoneCode(
  verificationId: verificationId,
  smsCode: '123456',
);

// Check biometric availability
final isAvailable = await enhancedAuth.isBiometricAvailable();

// Authenticate with biometrics
final success = await enhancedAuth.authenticateWithBiometrics();
```

### Watching Authentication State

```dart
// Watch authentication state
final authState = ref.watch(enhancedAuthNotifierProvider);

if (authState is AuthenticatedState) {
  // User is authenticated
  final user = authState.user;
  final provider = authState.provider;
} else if (authState is LoadingState) {
  // Show loading indicator
} else if (authState is ErrorState) {
  // Show error message
  final error = authState.message;
}
```

## 🔧 Troubleshooting

### Common Issues

1. **Google Sign-In Issues**:
   - Verify SHA-1 fingerprint is added to Firebase
   - Check package name matches Firebase project
   - Ensure GoogleService-Info.plist is added correctly

2. **Apple Sign-In Issues**:
   - Verify Apple Developer account setup
   - Check entitlements are configured
   - Test on physical iOS device

3. **Phone Authentication Issues**:
   - Check Firebase phone authentication is enabled
   - Verify phone number format (+country code)
   - Test with different phone numbers

4. **Biometric Authentication Issues**:
   - Check device supports biometrics
   - Verify permissions are granted
   - Test on physical device (not simulator)

### Debug Mode

Enable debug logging in development:

```dart
// Add to main.dart
import 'package:firebase_auth/firebase_auth.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Enable debug logging
  if (kDebugMode) {
    await FirebaseAuth.instance.useAuthEmulator('localhost', 9099);
  }
  
  // ... rest of initialization
}
```

## 🔒 Security Best Practices

1. **Environment Variables**: Never commit API keys to version control
2. **Certificate Pinning**: Implement certificate pinning for production
3. **Token Refresh**: Handle token refresh automatically
4. **Biometric Fallback**: Always provide alternative authentication
5. **Input Validation**: Validate all user inputs
6. **Error Handling**: Don't expose sensitive error details

## 📱 Platform-Specific Notes

### Android
- Minimum SDK version: 21 (Android 5.0)
- Google Play Services required for Google Sign-In
- Fingerprint hardware required for biometric auth

### iOS
- Minimum iOS version: 11.0
- Face ID requires iOS 11.0+
- Touch ID requires iOS 8.0+
- Sign in with Apple requires iOS 13.0+

### Web
- HTTPS required for all authentication methods
- reCAPTCHA required for phone authentication
- WebAuthn support for biometric authentication

## 🚀 Next Steps

1. **Configure Firebase project** with your actual credentials
2. **Update authentication providers** with real configuration
3. **Test on physical devices** for biometric authentication
4. **Implement error handling** for production scenarios
5. **Add analytics** to track authentication success rates
6. **Set up monitoring** for authentication failures

## 📚 Additional Resources

- [Firebase Auth Documentation](https://firebase.google.com/docs/auth)
- [Google Sign-In Documentation](https://developers.google.com/identity/sign-in/flutter)
- [Apple Sign-In Documentation](https://developer.apple.com/sign-in-with-apple/)
- [Local Auth Plugin Documentation](https://pub.dev/packages/local_auth)
- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)

---

Your enhanced authentication system is now ready! The implementation provides a comprehensive solution with multiple authentication methods, biometric support, and seamless user experience.
