# Flutter Options Data Integration Guide

## Table of Contents
1. [Setup](#setup)
2. [Data Models](#data-models)
3. [Services](#services)
4. [Widgets](#widgets)
5. [State Management](#state-management)
6. [Best Practices](#best-practices)

## Setup

### Dependencies
Add the following dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  supabase_flutter: ^2.3.4
  fl_chart: ^0.66.2
  syncfusion_flutter_datagrid: ^24.2.9
  syncfusion_flutter_charts: ^24.2.9
  provider: ^6.1.2
  intl: ^0.19.0
```

### Initialize Supabase Client

```dart
import 'package:supabase_flutter/supabase_flutter.dart';

Future<void> initializeSupabase() async {
  await Supabase.initialize(
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_ANON_KEY',
  );
}
```

## Data Models

### Option Data Model

```dart
class OptionData {
  final DateTime timestamp;
  final DateTime expiryDate;
  final double strikePrice;
  final double spotPrice;
  final String optionType;
  final double lastPrice;
  final int oi;
  final int volume;
  final double iv;
  final double delta;
  final double theta;
  final double? gamma;
  final double? vega;

  OptionData({
    required this.timestamp,
    required this.expiryDate,
    required this.strikePrice,
    required this.spotPrice,
    required this.optionType,
    required this.lastPrice,
    required this.oi,
    required this.volume,
    required this.iv,
    required this.delta,
    required this.theta,
    this.gamma,
    this.vega,
  });

  factory OptionData.fromJson(Map<String, dynamic> json) {
    return OptionData(
      timestamp: DateTime.parse(json['timestamp']),
      expiryDate: DateTime.parse(json['expiry_date']),
      strikePrice: json['strike_price'].toDouble(),
      spotPrice: json['spot_price'].toDouble(),
      optionType: json['option_type'],
      lastPrice: json['last_price'].toDouble(),
      oi: json['oi'],
      volume: json['volume'],
      iv: json['iv'].toDouble(),
      delta: json['delta'].toDouble(),
      theta: json['theta'].toDouble(),
      gamma: json['gamma']?.toDouble(),
      vega: json['vega']?.toDouble(),
    );
  }
}

class OHLCV {
  final DateTime bucket;
  final double open;
  final double high;
  final double low;
  final double close;
  final int volume;
  final int oi;
  final double spotPrice;
  final double avgIv;

  OHLCV({
    required this.bucket,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
    required this.oi,
    required this.spotPrice,
    required this.avgIv,
  });

  factory OHLCV.fromJson(Map<String, dynamic> json) {
    return OHLCV(
      bucket: DateTime.parse(json['bucket']),
      open: json['open'].toDouble(),
      high: json['high'].toDouble(),
      low: json['low'].toDouble(),
      close: json['close'].toDouble(),
      volume: json['volume'],
      oi: json['oi'],
      spotPrice: json['spot_price'].toDouble(),
      avgIv: json['avg_iv'].toDouble(),
    );
  }
}
```

## Services

### Options Service

```dart
class OptionsService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Fetch latest options chain
  Future<List<OptionData>> getLatestOptionsChain(DateTime expiryDate) async {
    final response = await _supabase
        .from('nifty_timeseries')
        .select()
        .eq('expiry_date', expiryDate.toIso8601String())
        .order('timestamp', ascending: false)
        .limit(1000);
    
    return (response as List).map((e) => OptionData.fromJson(e)).toList();
  }

  // Get 5-minute OHLCV data
  Future<List<OHLCV>> get5MinOHLCV({
    required DateTime expiryDate,
    required double strikePrice,
    required String optionType,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    final response = await _supabase
        .from('nifty_5min_ohlcv')
        .select()
        .eq('expiry_date', expiryDate.toIso8601String())
        .eq('strike_price', strikePrice)
        .eq('option_type', optionType)
        .gte('bucket', startTime?.toIso8601String())
        .lte('bucket', endTime?.toIso8601String())
        .order('bucket');
    
    return (response as List).map((e) => OHLCV.fromJson(e)).toList();
  }

  // Get IV term structure
  Future<Map<String, dynamic>> getIVTermStructure({
    DateTime? timestamp,
    int strikeOffset = 5,
  }) async {
    final response = await _supabase
        .rpc('get_iv_term_structure', params: {
          'p_timestamp': timestamp?.toIso8601String(),
          'p_strike_offset': strikeOffset,
        });
    
    return response;
  }

  // Get price movement data
  Future<List<OHLCV>> getPriceMovement({
    required DateTime expiryDate,
    required double strikePrice,
    required String optionType,
    String interval = '5 minutes',
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    final response = await _supabase
        .rpc('get_price_movement', params: {
          'p_expiry_date': expiryDate.toIso8601String(),
          'p_strike_price': strikePrice,
          'p_option_type': optionType,
          'p_interval': interval,
          'p_start_time': startTime?.toIso8601String(),
          'p_end_time': endTime?.toIso8601String(),
        });
    
    return (response as List).map((e) => OHLCV.fromJson(e)).toList();
  }
}
```

## Widgets

### Option Chain Table

```dart
class OptionChainTable extends StatelessWidget {
  final List<OptionData> optionsData;

  const OptionChainTable({Key? key, required this.optionsData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SfDataGrid(
      source: OptionChainDataSource(optionsData),
      columns: [
        GridColumn(columnName: 'strikePrice', label: Text('Strike')),
        GridColumn(columnName: 'lastPrice', label: Text('LTP')),
        GridColumn(columnName: 'oi', label: Text('OI')),
        GridColumn(columnName: 'volume', label: Text('Volume')),
        GridColumn(columnName: 'iv', label: Text('IV')),
        GridColumn(columnName: 'delta', label: Text('Delta')),
      ],
    );
  }
}
```

### IV Smile Chart

```dart
class IVSmileChart extends StatelessWidget {
  final List<OptionData> optionsData;

  const IVSmileChart({Key? key, required this.optionsData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SfCartesianChart(
      primaryXAxis: NumericAxis(title: AxisTitle(text: 'Strike Price')),
      primaryYAxis: NumericAxis(title: AxisTitle(text: 'IV')),
      series: <ChartSeries>[
        LineSeries<OptionData, double>(
          dataSource: optionsData.where((e) => e.optionType == 'CE').toList(),
          xValueMapper: (OptionData data, _) => data.strikePrice,
          yValueMapper: (OptionData data, _) => data.iv,
          name: 'Call IV',
        ),
        LineSeries<OptionData, double>(
          dataSource: optionsData.where((e) => e.optionType == 'PE').toList(),
          xValueMapper: (OptionData data, _) => data.strikePrice,
          yValueMapper: (OptionData data, _) => data.iv,
          name: 'Put IV',
        ),
      ],
    );
  }
}
```

### Price Chart

```dart
class PriceChart extends StatelessWidget {
  final List<OHLCV> ohlcvData;

  const PriceChart({Key? key, required this.ohlcvData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SfCartesianChart(
      primaryXAxis: DateTimeAxis(),
      primaryYAxis: NumericAxis(),
      series: <ChartSeries>[
        CandleSeries<OHLCV, DateTime>(
          dataSource: ohlcvData,
          xValueMapper: (OHLCV data, _) => data.bucket,
          lowValueMapper: (OHLCV data, _) => data.low,
          highValueMapper: (OHLCV data, _) => data.high,
          openValueMapper: (OHLCV data, _) => data.open,
          closeValueMapper: (OHLCV data, _) => data.close,
        ),
      ],
    );
  }
}
```

## State Management

### Options Provider

```dart
class OptionsProvider extends ChangeNotifier {
  final OptionsService _service = OptionsService();
  
  List<OptionData> _optionsChain = [];
  DateTime? _selectedExpiry;
  bool _isLoading = false;
  String? _error;

  List<OptionData> get optionsChain => _optionsChain;
  DateTime? get selectedExpiry => _selectedExpiry;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> fetchOptionsChain(DateTime expiry) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      _optionsChain = await _service.getLatestOptionsChain(expiry);
      _selectedExpiry = expiry;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<List<OHLCV>> fetchOHLCVData({
    required double strikePrice,
    required String optionType,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    if (_selectedExpiry == null) throw Exception('No expiry selected');
    
    return _service.get5MinOHLCV(
      expiryDate: _selectedExpiry!,
      strikePrice: strikePrice,
      optionType: optionType,
      startTime: startTime,
      endTime: endTime,
    );
  }
}
```

## Best Practices

1. **Error Handling**
   - Always wrap Supabase calls in try-catch blocks
   - Display meaningful error messages to users
   - Implement retry logic for network failures

2. **Data Caching**
   - Cache frequently accessed data like options chain
   - Implement local storage for offline access
   - Use Provider's state management for UI updates

3. **Performance**
   - Implement pagination for large datasets
   - Use lazy loading for charts and tables
   - Optimize re-renders using `const` constructors

4. **Real-time Updates**
   - Subscribe to database changes for live updates
   - Implement debouncing for frequent updates
   - Handle connection state changes gracefully

5. **UI/UX**
   - Show loading indicators during data fetch
   - Implement pull-to-refresh for manual updates
   - Add error boundaries for graceful error handling

## Example Usage

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initializeSupabase();
  
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => OptionsProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class OptionsChainScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<OptionsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const CircularProgressIndicator();
        }
        
        if (provider.error != null) {
          return Text('Error: ${provider.error}');
        }
        
        return Column(
          children: [
            OptionChainTable(optionsData: provider.optionsChain),
            IVSmileChart(optionsData: provider.optionsChain),
          ],
        );
      },
    );
  }
} 
4. Provide more detailed state management patterns? 